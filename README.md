# Audie Generator MS

Microserviço de geração de SQL usando IA, extraído de imagem Docker de produção AWS Lambda.

## 📋 Visão Geral

Este projeto é um microserviço FastAPI que utiliza IA (OpenAI/Vanna) para gerar consultas SQL a partir de linguagem natural. Foi originalmente desenvolvido para rodar como AWS Lambda Function.

### Funcionalidades Principais
- 🤖 Geração de SQL via IA (Vanna + OpenAI)
- 🗄️ Integração com MySQL/RDS
- 🚀 Compatível com AWS Lambda
- 📊 Processamento de consultas de auditoria

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FastAPI App   │───▶│   Vanna AI      │───▶│   OpenAI API    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────┐                            ┌─────────────────┐
│   AWS Lambda    │                            │   SQL Generation│
└─────────────────┘                            └─────────────────┘
         │                                              │
         ▼                                              ▼
┌─────────────────┐                            ┌─────────────────┐
│   MySQL RDS     │◀───────────────────────────│   Query Execution│
└─────────────────┘                            └─────────────────┘
```

## 🚀 Quick Start

### Pré-requisitos
- Python 3.12+
- Docker (opcional)
- PyCharm (recomendado)

### Configuração Rápida

1. **Clone/Configure o projeto**
   ```bash
   cd audie-generator-ms
   ```

2. **Configure variáveis de ambiente**
   ```bash
   cp .env.example .env
   # Edite o arquivo .env com suas credenciais
   ```

3. **Instale dependências**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # ou
   venv\Scripts\activate     # Windows
   
   pip install -r requirements.txt
   pip install 'uvicorn[standard]'
   pip install 'vanna[openai,mysql]'
   ```

4. **Execute localmente**
   ```bash
   uvicorn src.app:app --reload --host 0.0.0.0 --port 8000
   ```

### Usando Docker

```bash
# Build
docker build -f Dockerfile.reconstructed -t audie-generator-ms .

# Run
docker run -p 8080:8080 --env-file .env audie-generator-ms
```

## 📁 Estrutura do Projeto

```
audie-generator-ms/
├── src/                          # 📦 Código principal
│   ├── app.py                   # 🚀 Entry point da aplicação
│   ├── config.py                # ⚙️ Configurações
│   ├── controllers/             # 🎮 Controladores da API
│   │   └── completion_controller.py
│   ├── routes/                  # 🛣️ Definições de rotas
│   │   └── routes.py
│   └── services/                # 🔧 Serviços de negócio
│       ├── messages.py          # 💬 Processamento de mensagens
│       ├── openai_service.py    # 🤖 Integração OpenAI
│       ├── sql_service.py       # 🗄️ Serviços SQL
│       └── vanna_service.py     # 🧠 Integração Vanna AI
├── requirements.txt             # 📋 Dependências Python
├── Dockerfile.reconstructed     # 🐳 Dockerfile reconstruído
├── .env.example                 # 🔧 Template de configuração
├── PYCHARM_SETUP.md            # 💻 Guia PyCharm
└── DOCKER_RECONSTRUCTION.md    # 📚 Documentação da extração
```

## 🔧 Configuração do PyCharm

Para configuração detalhada do PyCharm, consulte: **[PYCHARM_SETUP.md](PYCHARM_SETUP.md)**

### Configuração Rápida PyCharm
1. **Open Project** → Selecione a pasta do projeto
2. **Configure Python Interpreter** → Use venv ou Docker
3. **Mark Directory as Sources Root** → Marque `src/`
4. **Configure Environment Variables** → Use arquivo `.env`

## 🔑 Variáveis de Ambiente

| Variável | Descrição | Exemplo |
|----------|-----------|---------|
| `MYSQL_HOST` | Host do banco MySQL | `localhost` |
| `MYSQL_DB` | Nome do banco | `audit_gold` |
| `MYSQL_USER` | Usuário do banco | `devdb` |
| `MYSQL_PASSWORD` | Senha do banco | `password` |
| `OPENAI_API_KEY` | Chave da API OpenAI | `sk-...` |
| `VANNA_API_KEY` | Chave da API Vanna | `abc123...` |
| `MODEL_SQL` | Modelo para geração SQL | `gpt-4.1-mini` |

## 🧪 Desenvolvimento

### Executar Testes
```bash
# TODO: Adicionar testes unitários
pytest tests/
```

### Debugging Local
```bash
# Com reload automático
uvicorn src.app:app --reload --log-level debug

# Ou via PyCharm
# Configure Run/Debug com src/app.py
```

### Logs
```bash
# Logs locais
tail -f logs/app.log

# Logs AWS (produção)
aws logs tail /aws/lambda/audie-generator-ms --follow
```

## 🚀 Deploy

### AWS Lambda
```bash
# Build para Lambda
docker build -f Dockerfile.reconstructed -t audie-generator-ms .

# Deploy via AWS CLI/SAM/Terraform
# (configuração específica do ambiente)
```

### Outras Plataformas
- **Heroku**: Usar `Procfile` com uvicorn
- **Google Cloud Run**: Usar Dockerfile.reconstructed
- **Azure Container Instances**: Compatível

## 📊 Monitoramento

### Métricas Importantes
- **Latência**: Tempo de geração SQL
- **Taxa de Erro**: Falhas na geração
- **Uso de API**: Consumo OpenAI/Vanna
- **Conexões DB**: Pool de conexões MySQL

### Health Checks
```bash
# Verificar saúde da aplicação
curl http://localhost:8000/health

# Verificar conectividade DB
curl http://localhost:8000/db/health
```

## 🔒 Segurança

### ⚠️ Credenciais Sensíveis
- **Nunca commitar** arquivos `.env`
- **Rotacionar chaves** regularmente
- **Usar AWS Secrets Manager** em produção
- **Limitar permissões** de banco de dados

### Boas Práticas
- Validação de input rigorosa
- Rate limiting para APIs
- Logs sem informações sensíveis
- Conexões DB com SSL

## 🤝 Contribuição

### Para Agentes de IA
Este projeto foi **extraído de uma imagem Docker** e pode ter limitações:
- ❌ Sem histórico Git
- ❌ Sem testes unitários visíveis
- ❌ Documentação limitada
- ✅ Código funcional de produção

### Melhorias Sugeridas
1. **Adicionar testes unitários**
2. **Implementar logging estruturado**
3. **Criar documentação de API**
4. **Refatorar configurações hardcoded**
5. **Adicionar health checks**

## 📚 Documentação Adicional

- **[PYCHARM_SETUP.md](PYCHARM_SETUP.md)** - Configuração completa PyCharm
- **[DOCKER_RECONSTRUCTION.md](DOCKER_RECONSTRUCTION.md)** - Como foi extraído da imagem
- **[.env.example](.env.example)** - Template de configuração

## 📄 Licença

Projeto extraído de ambiente de produção - verificar licenciamento original.

## 🆘 Suporte

Para dúvidas sobre:
- **Configuração**: Consulte PYCHARM_SETUP.md
- **Docker**: Consulte DOCKER_RECONSTRUCTION.md
- **Ambiente**: Verifique arquivo .env.example
