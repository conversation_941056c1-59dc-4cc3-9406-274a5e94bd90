2025-01-07T22:24:14Z DEBUG Librepo version: 1.8.0 with CURL_GLOBAL_ACK_EINTR support (libcurl/8.3.0 OpenSSL/1.0.2k-fips zlib/1.2.7 libidn2/2.3.0 libpsl/0.21.5 (+libidn2/2.3.0) libssh2/1.4.3 nghttp2/1.41.0 OpenLDAP/2.4.44)
2025-01-07T22:24:14Z DEBUG Current date: 2025-01-07T22:24:14+0000
2025-01-07T22:24:14Z DEBUG Librepo version: 1.8.0 with CURL_GLOBAL_ACK_EINTR support (libcurl/8.3.0 OpenSSL/1.0.2k-fips zlib/1.2.7 libidn2/2.3.0 libpsl/0.21.5 (+libidn2/2.3.0) libssh2/1.4.3 nghttp2/1.41.0 OpenLDAP/2.4.44)
2025-01-07T22:24:14Z DEBUG Current date: 2025-01-07T22:24:14+0000
2025-01-07T22:24:14Z DEBUG lr_handle_perform: Using dir: /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925
2025-01-07T22:24:14Z DEBUG lr_handle_perform: Using own SIGINT handler
2025-01-07T22:24:14Z DEBUG lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-01-07T22:24:14Z DEBUG lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-01-07T22:24:14Z DEBUG lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-01-07T22:24:14Z DEBUG lr_handle_perform: Downloading/Locating yum repo
2025-01-07T22:24:14Z DEBUG lr_yum_use_local: Locating repo..
2025-01-07T22:24:14Z DEBUG lr_yum_use_local_load_base: open(/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/repodata/repomd.xml): No such file or directory
2025-01-07T22:24:14Z DEBUG lr_handle_perform: Restoring an old SIGINT handler
2025-01-07T22:24:14Z DEBUG lr_handle_perform: Using dir: /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/tmpdir.4qHTEZ
2025-01-07T22:24:14Z DEBUG lr_handle_perform: Using own SIGINT handler
2025-01-07T22:24:14Z DEBUG lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-01-07T22:24:14Z DEBUG lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-01-07T22:24:14Z DEBUG lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-01-07T22:24:14Z DEBUG lr_handle_perform: Downloading/Locating yum repo
2025-01-07T22:24:14Z DEBUG lr_yum_download_remote: Downloading/Copying repo..
2025-01-07T22:24:14Z DEBUG lr_yum_download_repomd: Downloading repomd.xml via mirrorlist
2025-01-07T22:24:14Z DEBUG lr_download: Target: repodata/repomd.xml (-)
2025-01-07T22:24:14Z DEBUG lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0x3741af20
2025-01-07T22:24:14Z DEBUG lr_prepare_lrmirrors: Mirror: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/
2025-01-07T22:24:14Z DEBUG select_next_target: Selecting mirror for: repodata/repomd.xml
2025-01-07T22:24:14Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/repomd.xml
2025-01-07T22:24:14Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (fd: 14): Operation not supported
2025-01-07T22:24:14Z DEBUG lr_download: Downloading started
2025-01-07T22:24:14Z DEBUG check_transfer_statuses: Transfer finished: repodata/repomd.xml (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/repomd.xml)
2025-01-07T22:24:14Z DEBUG lr_yum_download_remote: Parsing repomd.xml
2025-01-07T22:24:14Z DEBUG 1736121600: Repomd revision: lr_yum_download_remote
2025-01-07T22:24:14Z DEBUG lr_download: Target: repodata/primary.xml.gz (-)
2025-01-07T22:24:14Z DEBUG lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0x3741af20
2025-01-07T22:24:14Z DEBUG lr_prepare_lrmirrors: Mirror: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/
2025-01-07T22:24:14Z DEBUG lr_download: Target: repodata/filelists.xml.gz (-)
2025-01-07T22:24:14Z DEBUG lr_download: Target: repodata/updateinfo.xml.gz (-)
2025-01-07T22:24:14Z DEBUG lr_download: Target: repodata/comps.xml.gz (-)
2025-01-07T22:24:14Z DEBUG select_next_target: Selecting mirror for: repodata/primary.xml.gz
2025-01-07T22:24:14Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/primary.xml.gz
2025-01-07T22:24:14Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (fd: 18): Operation not supported
2025-01-07T22:24:14Z DEBUG select_next_target: Selecting mirror for: repodata/filelists.xml.gz
2025-01-07T22:24:14Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/filelists.xml.gz
2025-01-07T22:24:14Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (fd: 19): Operation not supported
2025-01-07T22:24:14Z DEBUG select_next_target: Selecting mirror for: repodata/updateinfo.xml.gz
2025-01-07T22:24:14Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/updateinfo.xml.gz
2025-01-07T22:24:14Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (fd: 20): Operation not supported
2025-01-07T22:24:14Z DEBUG lr_download: Downloading started
2025-01-07T22:24:14Z DEBUG check_transfer_statuses: Transfer finished: repodata/updateinfo.xml.gz (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/updateinfo.xml.gz)
2025-01-07T22:24:14Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 532d2f23661b6bb73642708f8c6b6c5ea72d9087ed827e18290d7c38e34a67aa is OK
2025-01-07T22:24:14Z DEBUG select_next_target: Selecting mirror for: repodata/comps.xml.gz
2025-01-07T22:24:14Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/comps.xml.gz
2025-01-07T22:24:14Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (fd: 20): Operation not supported
2025-01-07T22:24:14Z DEBUG check_transfer_statuses: Transfer finished: repodata/comps.xml.gz (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/comps.xml.gz)
2025-01-07T22:24:14Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) b7dbcd7ccc6a655cdaa9908316e089af02261a13564100562c9708cd8f5ba1a9 is OK
2025-01-07T22:24:14Z DEBUG check_transfer_statuses: Transfer finished: repodata/primary.xml.gz (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/primary.xml.gz)
2025-01-07T22:24:14Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) f382ad3a20fd5294838fea7be67a09cca0c44ea15dfa913355dd590cef536d84 is OK
2025-01-07T22:24:15Z DEBUG check_transfer_statuses: Transfer finished: repodata/filelists.xml.gz (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/filelists.xml.gz)
2025-01-07T22:24:15Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 71a28bb6a40a7b58071b323921a55e363c63437b4edfbe8af4d02d2c8a57117e is OK
2025-01-07T22:24:15Z DEBUG lr_handle_perform: Restoring an old SIGINT handler
2025-01-07T22:24:15Z DEBUG lr_handle_perform: Using dir: /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925
2025-01-07T22:24:15Z DEBUG lr_handle_perform: Using own SIGINT handler
2025-01-07T22:24:15Z DEBUG lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-01-07T22:24:15Z DEBUG lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-01-07T22:24:15Z DEBUG lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-01-07T22:24:15Z DEBUG lr_handle_perform: Downloading/Locating yum repo
2025-01-07T22:24:15Z DEBUG lr_yum_use_local: Locating repo..
2025-01-07T22:24:15Z DEBUG lr_yum_use_local_load_base: Parsing repomd.xml
2025-01-07T22:24:15Z DEBUG lr_yum_use_local_load_base: Repomd revision: 1736121600
2025-01-07T22:24:15Z DEBUG lr_yum_use_local: Repository was successfully located
2025-01-07T22:24:15Z DEBUG lr_yum_check_checksum_of_md_record: Checking checksum of /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/repodata/primary.xml.gz (expected: f382ad3a20fd5294838fea7be67a09cca0c44ea15dfa913355dd590cef536d84 [sha256])
2025-01-07T22:24:15Z DEBUG lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-01-07T22:24:15Z DEBUG lr_yum_check_checksum_of_md_record: Checking checksum of /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/repodata/filelists.xml.gz (expected: 71a28bb6a40a7b58071b323921a55e363c63437b4edfbe8af4d02d2c8a57117e [sha256])
2025-01-07T22:24:15Z DEBUG lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-01-07T22:24:15Z DEBUG lr_yum_check_checksum_of_md_record: Checking checksum of /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/repodata/updateinfo.xml.gz (expected: 532d2f23661b6bb73642708f8c6b6c5ea72d9087ed827e18290d7c38e34a67aa [sha256])
2025-01-07T22:24:15Z DEBUG lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-01-07T22:24:15Z DEBUG lr_yum_check_checksum_of_md_record: Checking checksum of /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/repodata/comps.xml.gz (expected: b7dbcd7ccc6a655cdaa9908316e089af02261a13564100562c9708cd8f5ba1a9 [sha256])
2025-01-07T22:24:15Z DEBUG lr_yum_check_checksum_of_md_record: Checksum check - Passed
2025-01-07T22:24:15Z DEBUG lr_handle_perform: Restoring an old SIGINT handler
2025-01-07T22:24:25Z DEBUG lr_download_packages: Using own SIGINT handler
2025-01-07T22:24:25Z DEBUG lr_handle_prepare_internal_mirrorlist: Preparing internal mirrorlist
2025-01-07T22:24:25Z DEBUG lr_handle_prepare_internal_mirrorlist: Finalizing internal mirrorlist
2025-01-07T22:24:25Z DEBUG lr_handle_prepare_internal_mirrorlist: Finalizing mirrors reported via LRI_MIRRORS
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/e02466195d382ceb7e4e086f0ec7212976e09e5d0c9e698e7b9ac223699dcbd9/alternatives-1.15-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_prepare_lrmirrors: Preparing internal mirror list for handle id: 0x37483190
2025-01-07T22:24:25Z DEBUG lr_prepare_lrmirrors: Mirror: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/d2c376dc8fadf0b38751fc9d5698b98a68b9328491154ade85acfc86ef88a7af/amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/d60aa62ecfb9b43cf30ae8f115731c2260a3bedf03f4e8577c09c3d69225846a/audit-libs-3.0.6-1.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/eabf74784cf22ff6408b607fd0c29ded1918cca9e5e89895407222f7eb0d59f3/basesystem-11-11.amzn2023.0.2.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/e2c64639b6ea4c66bc9231f587699ca6f32840805a38fd5172f028cc02c02782/bash-5.2.15-1.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/869a148f83822292f1a5598e9d64716745edf1bf5cb0c260ac5ca5c8e7757e3e/bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/2e10c5ce79865c77ae75ddd13198a67b4c93970ddb46df2e6cc7a3d02fdbd3da/ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/a32e21300dd48a7c08d63dfdc9efaa2f89d62703f6e453a0cfe4c97e5fbb64ba/coreutils-single-8.32-30.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/fa28d623a46cbb1414dd66fb3bd3f9a54040d7a2e1e7ead5f77eea833dc4385d/crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/2f6892cf8af686a11197de1cad7a3a3d64989b637deedd0e81065f47e2a0e2a9/curl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/72df28347ffbd7dada02264371123e2a7d7d0118d1b26a2dfbed86936812abd5/dnf-data-4.14.0-1.amzn2023.0.5.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/b8f81445a16a2761dee1d21c5aea376fdab0c6a8fec5c303708dae93f38b4c52/file-libs-5.39-7.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/116c7b2f4190c1cf35a1c8cd09d350f648fdc6fc2b9dab936b19ff620ce1a62b/filesystem-3.14-5.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/d7792d0cfcd562493bf374b7da35c925bec8768098d49f65128056ac861ad253/gawk-5.1.0-3.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/370ed4e53e18fc5d2302a00c08683eec3307e7923307b36bd0d02838d126af7e/glib2-2.82.2-764.amzn2023.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/d03313d86163466e15d75cb78060700fc9048399f52095c7666e36536fb633fa/glibc-2.34-117.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/ce8b7cf8901e2bca06284caeb63cfafa81bd6ed5f0c7cba94de8845c50659ebe/glibc-common-2.34-117.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/382ecc64a97705ab1d4c414085783ffe3c7aa85cc7b0912adfdc19e477a826af/glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/1a22a34d001b63ad0513f9b79f5f3031056c5abe07a062ce27c69a1f04548a5f/gmp-6.2.1-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/94cbc3817f1a339001e6d255735bb1f16479e4f19961234e387b66bf96d0b8d6/gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/729fb71fb6a55415c48d8454d69229ffea3df5bb7263fa99a05b7285212271e5/gobject-introspection-1.82.0-1.amzn2023.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/e13a56ba488395b5094bc2a67b0b236b4a2d69f3ea8d8263ed26a4ea5116bac1/gpgme-1.15.1-6.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/0814a139bce5760c62d9f207c2b0209aecddebedbfc8c5bb645a11b70cab86f6/grep-3.8-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/52a84086dc06dd09a9b8638199ba7fdfe36c49635174516a3f8bbe5634c20b23/json-c-0.14-8.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/6075dac99ce4a3e9d301505db13af89cd8456b20f59f0c7dc71b3e3f5b80c64e/keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/a9c341b966b72b355cc91f59a8602a7fd0fec51d32943dda778c601900b5fdce/krb5-libs-1.21.3-1.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/e259da69c6475bf8fc1a4eef88a7edd05d76f94e9141419b8c00d9edb63c5547/libacl-2.3.1-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/7fa6013f391984775e3e939e76a1f837e7ba68f9e00b0de2d8dee5ae09ab5b76/libarchive-3.7.4-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/c8fdc4842537be6e3eead012b2e0e4e3d9f78b10b1c8ef36a51206300fada7a8/libassuan-2.5.5-1.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/8d932bb4f8760a16b8526821284078fbb6c272129f0cf806c591ebf3b6b26263/libattr-2.5.1-3.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/564a0b2f623ba289e8de6b6cf6156808c69a1866e3154a8efa41a6fe0a1c776a/libblkid-2.37.4-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/c6614dfdd952c1a9ca9e78bf2159d7a77e9bf6d14ceacf85e10e03e701e218f6/libcap-2.48-2.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/8a64fba4e0664e827312edb312cc519ab78214b7ddb799d1a053f27108a9116a/libcap-ng-0.8.2-4.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/7b0044d75eac628eb78a8185cef51e3b34dccd67fa69e8735ec7ddf9a770ff00/libcom_err-1.46.5-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/a6658d92f67e9a027422c2a82f052fd8283ed87633f8e6ecd7db520a689f5c53/libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/a4767eaed98fa3f71ea40e3758925aadf85db5a82b5241b89278c28003fa882c/libdnf-0.69.0-8.amzn2023.0.5.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/f4a8786ffc5b417a99db9bb55b4268bab7909cf3197aa8443ba8bb344bfbdaf2/libffi-3.4.4-1.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/9d60c83f19a3ee144bb15c201be4aca0f5a6a2e732d8f6da29f9dcd69d9b8f2d/libgcc-11.4.1-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/e78957b2c808b1b1c6424c10ae9fb2c7efd997d8035eaadf42c118a3c10e6457/libgcrypt-1.10.2-1.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/77ae94cd73f999226cdf76c35fdeec0025e350d4d326ce644903318042c23713/libgpg-error-1.42-1.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/ff67776790e736d3669ef87cfb1987f018751b024fb45e69717f4ec4e9f9eb22/libidn2-2.3.2-1.amzn2023.0.5.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/17299fcc88abf0f40fdd7de8841b267dd76c91d22307b64a433a0e20f64e7ba1/libmodulemd-2.13.0-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/32ac1afe36ec79eeb9d6af17142a4160cab24c8275c82e450c139d871ec0c951/libmount-2.37.4-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/9773158c5e26caa9c7ac413ca7576c7917a9cce34a55e633d16a9952221c0927/libnghttp2-1.59.0-3.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/79d9e773316ae2228e40b77989bc3a2f49906d106d70a70fce7b6cd5526ea81e/libpeas-1.32.0-1.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/a851c067adbf9a57bc282ea3a9c470bc82e32b4215eea2a4bc5106ae40b1d748/libpsl-0.21.1-3.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/3f6a5587ca21a65ab7c30e31a3bfa5772df61ff2938ceaaf93b312d011699fc9/librepo-1.14.5-2.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/d93a5c5d224a31d019187e37a971f9e7f6dcd1225080bffa7187bda69e3907de/libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/3f6d54aef848223d05ca20ebd55c9d6416d708d8b2578de7717f00fbb664d27e/libselinux-3.4-5.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/d24d097da83144de4bc48e649e32ecd7b61c31ef2d9b8b88146bdaaf8e574b95/libsepol-3.4-3.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/15742c87d59656ef05a91999871d239a8fb5452f81165e22426e120f932b333e/libsigsegv-2.13-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/6fc160eb5a7de838f2f7efc78fc191bdc7623091ca67ed052a28081da97beec6/libsmartcols-2.37.4-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/3fc85db75835b774cc04137e92775ceb3f44ccdb43d54dd2f7b3ed30b2cdc1f0/libsolv-0.7.22-1.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/79190d8096474765b33d21369f1f3ba72613d97fbd7779ee8f3afe1b96fe4b3b/libstdc%2b%2b-11.4.1-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/ed01d6bc4d5a75dcef9f983b771051eecf20a08519d78517c6668c3278a147fb/libtasn1-4.19.0-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/db7d52a4bbccbf0ce7febf6018b25403e617dfc7060ac9f609544848100ddbfd/libunistring-0.9.10-10.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/e768518d278e30b71d9e52f7eb72577e948f701b75d8f837db4a9718eb8d975a/libuuid-2.37.4-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/66a901c5895106101303eecb2c874d2615453357af767bfe4e6ee056bb1b4f7f/libverto-0.3.2-1.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/c411c2ed7dc7469d2c84d90f75953e1f00a659951fed0f82adee895ac23bf89c/libxml2-2.10.4-1.amzn2023.0.7.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/90665839c478bbbbd6e2e8eb3cf1484a1d95f6cd66dc739a049777aa52f358b8/libyaml-0.2.5-5.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/7df4dbe167421445491e7bf93a8be9caea1800c3a382ccb4da60b18ad542ca6c/libzstd-1.5.5-1.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/d16303135f65cdeb1291691bba5f4b74c68015ac0418a808ad1385799298401b/lua-libs-5.4.4-3.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/9a2ffcf61798358c0d59de3177d6b44bfd31702eaf00d19b4ddec2ba4228b3ff/lz4-libs-1.9.4-1.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/6bcbbf424df9489e25122b7c4e871e6c8a40241cd764cf1d13435100a0689b5b/microdnf-3.10.0-2.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/2a0c4b17c3c3060a292c056ac111b204b8f77572be737a2c637f3249ee133c64/microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/3162b14361101503c33b40ecda4bc766fbd97635e5f671c79699c22c2a735af6/mpfr-4.1.0-7.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/cf22ad6a84e56f5172a4b28ead36c11274adf48a9b9f080fd4e5154444737ea0/ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/7bd0fba806b5c1408e865166f8f8b98c7c0695105dbb58e41fd5b4759a38b96f/ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/15918f7b5853a125fbcb31fb6ff80c1a24e0604e4cd5806458d5ef31f5a90d18/npth-1.6-6.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/d2b8f944c88cfb8804ebe2e4b4e4d32e108ef4573e1197df1ba5e3b75737e70b/openssl-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/ee0c06ab41a43f9b77ddc905ae266093cb3afcad9ca681740e8f483372891c91/p11-kit-0.24.1-2.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/0ce1183338177106f0d5ec41ffb9928d2220d5c758a482aa286eeb6b0356b0b4/p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/54796fd3055837baaadc9156b2135d4e07d102fc1c85a400d9cf13245a3e7871/pcre2-10.40-1.amzn2023.0.3.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/1ab189dde1bf9f401f32498a8a51b22a85a2963e6bf4a4dcedb25adb8bb48529/pcre2-syntax-10.40-1.amzn2023.0.3.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/131a82482a33aa8c0351161f72351dfaf994bf501235c662bdc40ae53658ea8b/popt-1.18-6.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/fe51b2668e626bf16dcc9bb8f17c6825e5ca59839466e49bca72d5940f690b1e/publicsuffix-list-dafsa-20240212-61.amzn2023.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/b17aa6231896adb077ec1ca1dd8ae19168fdfee4b2b156998e163eef45bfc48b/readline-8.1-2.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/09468e67b6afdff8d25ea577efa589a1810e15d35b89197fa9c3e2f8eb09fd55/rpm-4.16.1.3-29.amzn2023.0.6.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/25ca74315525e917f8636a29779d49b2be394b16f51985eb31f05aacd1a8647b/rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/ec9ced376b62d2c3682ac2b5edbae9c4312617d8b2f37b386d2e8de4de0f04e1/sed-4.8-7.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/32505f07050b1e1f76a5ed0eec0af03f773d27ccf6f6c01e8a7d32c583b5593f/setup-2.13.7-3.amzn2023.0.2.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/06990a4e4d12bd014cc064846d1f8ee69f2d885d70949baeee6ca75d4050c233/sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/0c4aab2f11620d58c3520b50d8c58fefe6beea597fa14227bf07393223380b7e/system-release-2023.6.20250107-0.amzn2023.noarch.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/a89ed2c87b25bc505ca2125606bfc61e8802729c41a5c074add0905fb750b126/xz-libs-5.2.5-9.amzn2023.0.2.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG lr_download: Target: ../../../../blobstore/cd43f0c4767609d6d231d27613da70672ee8a444a9ebf03d766e77fa536131f7/zlib-1.2.11-33.amzn2023.0.5.aarch64.rpm (-)
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/e02466195d382ceb7e4e086f0ec7212976e09e5d0c9e698e7b9ac223699dcbd9/alternatives-1.15-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/e02466195d382ceb7e4e086f0ec7212976e09e5d0c9e698e7b9ac223699dcbd9/alternatives-1.15-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/alternatives-1.15-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/d2c376dc8fadf0b38751fc9d5698b98a68b9328491154ade85acfc86ef88a7af/amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d2c376dc8fadf0b38751fc9d5698b98a68b9328491154ade85acfc86ef88a7af/amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/d60aa62ecfb9b43cf30ae8f115731c2260a3bedf03f4e8577c09c3d69225846a/audit-libs-3.0.6-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d60aa62ecfb9b43cf30ae8f115731c2260a3bedf03f4e8577c09c3d69225846a/audit-libs-3.0.6-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/audit-libs-3.0.6-1.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_download: Downloading started
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "9055" (converted 9055/9055 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/d2c376dc8fadf0b38751fc9d5698b98a68b9328491154ade85acfc86ef88a7af/amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/d2c376dc8fadf0b38751fc9d5698b98a68b9328491154ade85acfc86ef88a7af/amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) d2c376dc8fadf0b38751fc9d5698b98a68b9328491154ade85acfc86ef88a7af is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/eabf74784cf22ff6408b607fd0c29ded1918cca9e5e89895407222f7eb0d59f3/basesystem-11-11.amzn2023.0.2.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/eabf74784cf22ff6408b607fd0c29ded1918cca9e5e89895407222f7eb0d59f3/basesystem-11-11.amzn2023.0.2.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/basesystem-11-11.amzn2023.0.2.noarch.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "118766" (converted 118766/118766 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/d60aa62ecfb9b43cf30ae8f115731c2260a3bedf03f4e8577c09c3d69225846a/audit-libs-3.0.6-1.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/d60aa62ecfb9b43cf30ae8f115731c2260a3bedf03f4e8577c09c3d69225846a/audit-libs-3.0.6-1.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) d60aa62ecfb9b43cf30ae8f115731c2260a3bedf03f4e8577c09c3d69225846a is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/e2c64639b6ea4c66bc9231f587699ca6f32840805a38fd5172f028cc02c02782/bash-5.2.15-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/e2c64639b6ea4c66bc9231f587699ca6f32840805a38fd5172f028cc02c02782/bash-5.2.15-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/bash-5.2.15-1.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "36415" (converted 36415/36415 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/e02466195d382ceb7e4e086f0ec7212976e09e5d0c9e698e7b9ac223699dcbd9/alternatives-1.15-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/e02466195d382ceb7e4e086f0ec7212976e09e5d0c9e698e7b9ac223699dcbd9/alternatives-1.15-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) e02466195d382ceb7e4e086f0ec7212976e09e5d0c9e698e7b9ac223699dcbd9 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/869a148f83822292f1a5598e9d64716745edf1bf5cb0c260ac5ca5c8e7757e3e/bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/869a148f83822292f1a5598e9d64716745edf1bf5cb0c260ac5ca5c8e7757e3e/bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "1848530" (converted 1848530/1848530 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/e2c64639b6ea4c66bc9231f587699ca6f32840805a38fd5172f028cc02c02782/bash-5.2.15-1.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/e2c64639b6ea4c66bc9231f587699ca6f32840805a38fd5172f028cc02c02782/bash-5.2.15-1.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) e2c64639b6ea4c66bc9231f587699ca6f32840805a38fd5172f028cc02c02782 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/2e10c5ce79865c77ae75ddd13198a67b4c93970ddb46df2e6cc7a3d02fdbd3da/ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/2e10c5ce79865c77ae75ddd13198a67b4c93970ddb46df2e6cc7a3d02fdbd3da/ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "7969" (converted 7969/7969 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/eabf74784cf22ff6408b607fd0c29ded1918cca9e5e89895407222f7eb0d59f3/basesystem-11-11.amzn2023.0.2.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/eabf74784cf22ff6408b607fd0c29ded1918cca9e5e89895407222f7eb0d59f3/basesystem-11-11.amzn2023.0.2.noarch.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) eabf74784cf22ff6408b607fd0c29ded1918cca9e5e89895407222f7eb0d59f3 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/a32e21300dd48a7c08d63dfdc9efaa2f89d62703f6e453a0cfe4c97e5fbb64ba/coreutils-single-8.32-30.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/a32e21300dd48a7c08d63dfdc9efaa2f89d62703f6e453a0cfe4c97e5fbb64ba/coreutils-single-8.32-30.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/coreutils-single-8.32-30.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "45378" (converted 45378/45378 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/869a148f83822292f1a5598e9d64716745edf1bf5cb0c260ac5ca5c8e7757e3e/bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/869a148f83822292f1a5598e9d64716745edf1bf5cb0c260ac5ca5c8e7757e3e/bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 869a148f83822292f1a5598e9d64716745edf1bf5cb0c260ac5ca5c8e7757e3e is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/fa28d623a46cbb1414dd66fb3bd3f9a54040d7a2e1e7ead5f77eea833dc4385d/crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/fa28d623a46cbb1414dd66fb3bd3f9a54040d7a2e1e7ead5f77eea833dc4385d/crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "752632" (converted 752632/752632 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/2e10c5ce79865c77ae75ddd13198a67b4c93970ddb46df2e6cc7a3d02fdbd3da/ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/2e10c5ce79865c77ae75ddd13198a67b4c93970ddb46df2e6cc7a3d02fdbd3da/ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 2e10c5ce79865c77ae75ddd13198a67b4c93970ddb46df2e6cc7a3d02fdbd3da is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/2f6892cf8af686a11197de1cad7a3a3d64989b637deedd0e81065f47e2a0e2a9/curl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/2f6892cf8af686a11197de1cad7a3a3d64989b637deedd0e81065f47e2a0e2a9/curl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/curl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "623157" (converted 623157/623157 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/a32e21300dd48a7c08d63dfdc9efaa2f89d62703f6e453a0cfe4c97e5fbb64ba/coreutils-single-8.32-30.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/a32e21300dd48a7c08d63dfdc9efaa2f89d62703f6e453a0cfe4c97e5fbb64ba/coreutils-single-8.32-30.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) a32e21300dd48a7c08d63dfdc9efaa2f89d62703f6e453a0cfe4c97e5fbb64ba is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/72df28347ffbd7dada02264371123e2a7d7d0118d1b26a2dfbed86936812abd5/dnf-data-4.14.0-1.amzn2023.0.5.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/72df28347ffbd7dada02264371123e2a7d7d0118d1b26a2dfbed86936812abd5/dnf-data-4.14.0-1.amzn2023.0.5.noarch.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/dnf-data-4.14.0-1.amzn2023.0.5.noarch.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "61229" (converted 61229/61229 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/fa28d623a46cbb1414dd66fb3bd3f9a54040d7a2e1e7ead5f77eea833dc4385d/crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/fa28d623a46cbb1414dd66fb3bd3f9a54040d7a2e1e7ead5f77eea833dc4385d/crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) fa28d623a46cbb1414dd66fb3bd3f9a54040d7a2e1e7ead5f77eea833dc4385d is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/b8f81445a16a2761dee1d21c5aea376fdab0c6a8fec5c303708dae93f38b4c52/file-libs-5.39-7.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/b8f81445a16a2761dee1d21c5aea376fdab0c6a8fec5c303708dae93f38b4c52/file-libs-5.39-7.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/file-libs-5.39-7.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "34864" (converted 34864/34864 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/72df28347ffbd7dada02264371123e2a7d7d0118d1b26a2dfbed86936812abd5/dnf-data-4.14.0-1.amzn2023.0.5.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/72df28347ffbd7dada02264371123e2a7d7d0118d1b26a2dfbed86936812abd5/dnf-data-4.14.0-1.amzn2023.0.5.noarch.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 72df28347ffbd7dada02264371123e2a7d7d0118d1b26a2dfbed86936812abd5 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/116c7b2f4190c1cf35a1c8cd09d350f648fdc6fc2b9dab936b19ff620ce1a62b/filesystem-3.14-5.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/116c7b2f4190c1cf35a1c8cd09d350f648fdc6fc2b9dab936b19ff620ce1a62b/filesystem-3.14-5.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/filesystem-3.14-5.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "160121" (converted 160121/160121 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/2f6892cf8af686a11197de1cad7a3a3d64989b637deedd0e81065f47e2a0e2a9/curl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/2f6892cf8af686a11197de1cad7a3a3d64989b637deedd0e81065f47e2a0e2a9/curl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 2f6892cf8af686a11197de1cad7a3a3d64989b637deedd0e81065f47e2a0e2a9 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/d7792d0cfcd562493bf374b7da35c925bec8768098d49f65128056ac861ad253/gawk-5.1.0-3.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d7792d0cfcd562493bf374b7da35c925bec8768098d49f65128056ac861ad253/gawk-5.1.0-3.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gawk-5.1.0-3.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "22058" (converted 22058/22058 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/116c7b2f4190c1cf35a1c8cd09d350f648fdc6fc2b9dab936b19ff620ce1a62b/filesystem-3.14-5.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/116c7b2f4190c1cf35a1c8cd09d350f648fdc6fc2b9dab936b19ff620ce1a62b/filesystem-3.14-5.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 116c7b2f4190c1cf35a1c8cd09d350f648fdc6fc2b9dab936b19ff620ce1a62b is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/370ed4e53e18fc5d2302a00c08683eec3307e7923307b36bd0d02838d126af7e/glib2-2.82.2-764.amzn2023.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/370ed4e53e18fc5d2302a00c08683eec3307e7923307b36bd0d02838d126af7e/glib2-2.82.2-764.amzn2023.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/glib2-2.82.2-764.amzn2023.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "601129" (converted 601129/601129 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/b8f81445a16a2761dee1d21c5aea376fdab0c6a8fec5c303708dae93f38b4c52/file-libs-5.39-7.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/b8f81445a16a2761dee1d21c5aea376fdab0c6a8fec5c303708dae93f38b4c52/file-libs-5.39-7.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) b8f81445a16a2761dee1d21c5aea376fdab0c6a8fec5c303708dae93f38b4c52 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/d03313d86163466e15d75cb78060700fc9048399f52095c7666e36536fb633fa/glibc-2.34-117.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d03313d86163466e15d75cb78060700fc9048399f52095c7666e36536fb633fa/glibc-2.34-117.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/glibc-2.34-117.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "1007193" (converted 1007193/1007193 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/d7792d0cfcd562493bf374b7da35c925bec8768098d49f65128056ac861ad253/gawk-5.1.0-3.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/d7792d0cfcd562493bf374b7da35c925bec8768098d49f65128056ac861ad253/gawk-5.1.0-3.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) d7792d0cfcd562493bf374b7da35c925bec8768098d49f65128056ac861ad253 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/ce8b7cf8901e2bca06284caeb63cfafa81bd6ed5f0c7cba94de8845c50659ebe/glibc-common-2.34-117.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/ce8b7cf8901e2bca06284caeb63cfafa81bd6ed5f0c7cba94de8845c50659ebe/glibc-common-2.34-117.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/glibc-common-2.34-117.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "3162692" (converted 3162692/3162692 expected)
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "1772119" (converted 1772119/1772119 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/d03313d86163466e15d75cb78060700fc9048399f52095c7666e36536fb633fa/glibc-2.34-117.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/d03313d86163466e15d75cb78060700fc9048399f52095c7666e36536fb633fa/glibc-2.34-117.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) d03313d86163466e15d75cb78060700fc9048399f52095c7666e36536fb633fa is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/382ecc64a97705ab1d4c414085783ffe3c7aa85cc7b0912adfdc19e477a826af/glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/382ecc64a97705ab1d4c414085783ffe3c7aa85cc7b0912adfdc19e477a826af/glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/370ed4e53e18fc5d2302a00c08683eec3307e7923307b36bd0d02838d126af7e/glib2-2.82.2-764.amzn2023.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/370ed4e53e18fc5d2302a00c08683eec3307e7923307b36bd0d02838d126af7e/glib2-2.82.2-764.amzn2023.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 370ed4e53e18fc5d2302a00c08683eec3307e7923307b36bd0d02838d126af7e is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/1a22a34d001b63ad0513f9b79f5f3031056c5abe07a062ce27c69a1f04548a5f/gmp-6.2.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/1a22a34d001b63ad0513f9b79f5f3031056c5abe07a062ce27c69a1f04548a5f/gmp-6.2.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gmp-6.2.1-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "21041" (converted 21041/21041 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/382ecc64a97705ab1d4c414085783ffe3c7aa85cc7b0912adfdc19e477a826af/glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/382ecc64a97705ab1d4c414085783ffe3c7aa85cc7b0912adfdc19e477a826af/glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 382ecc64a97705ab1d4c414085783ffe3c7aa85cc7b0912adfdc19e477a826af is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/94cbc3817f1a339001e6d255735bb1f16479e4f19961234e387b66bf96d0b8d6/gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/94cbc3817f1a339001e6d255735bb1f16479e4f19961234e387b66bf96d0b8d6/gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "291129" (converted 291129/291129 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/ce8b7cf8901e2bca06284caeb63cfafa81bd6ed5f0c7cba94de8845c50659ebe/glibc-common-2.34-117.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/ce8b7cf8901e2bca06284caeb63cfafa81bd6ed5f0c7cba94de8845c50659ebe/glibc-common-2.34-117.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) ce8b7cf8901e2bca06284caeb63cfafa81bd6ed5f0c7cba94de8845c50659ebe is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/729fb71fb6a55415c48d8454d69229ffea3df5bb7263fa99a05b7285212271e5/gobject-introspection-1.82.0-1.amzn2023.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/729fb71fb6a55415c48d8454d69229ffea3df5bb7263fa99a05b7285212271e5/gobject-introspection-1.82.0-1.amzn2023.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gobject-introspection-1.82.0-1.amzn2023.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "421132" (converted 421132/421132 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/94cbc3817f1a339001e6d255735bb1f16479e4f19961234e387b66bf96d0b8d6/gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/94cbc3817f1a339001e6d255735bb1f16479e4f19961234e387b66bf96d0b8d6/gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 94cbc3817f1a339001e6d255735bb1f16479e4f19961234e387b66bf96d0b8d6 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/e13a56ba488395b5094bc2a67b0b236b4a2d69f3ea8d8263ed26a4ea5116bac1/gpgme-1.15.1-6.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/e13a56ba488395b5094bc2a67b0b236b4a2d69f3ea8d8263ed26a4ea5116bac1/gpgme-1.15.1-6.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gpgme-1.15.1-6.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "282313" (converted 282313/282313 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/1a22a34d001b63ad0513f9b79f5f3031056c5abe07a062ce27c69a1f04548a5f/gmp-6.2.1-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/1a22a34d001b63ad0513f9b79f5f3031056c5abe07a062ce27c69a1f04548a5f/gmp-6.2.1-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 1a22a34d001b63ad0513f9b79f5f3031056c5abe07a062ce27c69a1f04548a5f is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/0814a139bce5760c62d9f207c2b0209aecddebedbfc8c5bb645a11b70cab86f6/grep-3.8-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/0814a139bce5760c62d9f207c2b0209aecddebedbfc8c5bb645a11b70cab86f6/grep-3.8-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/grep-3.8-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "208479" (converted 208479/208479 expected)
2025-01-07T22:24:25Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/e13a56ba488395b5094bc2a67b0b236b4a2d69f3ea8d8263ed26a4ea5116bac1/gpgme-1.15.1-6.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/e13a56ba488395b5094bc2a67b0b236b4a2d69f3ea8d8263ed26a4ea5116bac1/gpgme-1.15.1-6.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:25Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) e13a56ba488395b5094bc2a67b0b236b4a2d69f3ea8d8263ed26a4ea5116bac1 is OK
2025-01-07T22:24:25Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/52a84086dc06dd09a9b8638199ba7fdfe36c49635174516a3f8bbe5634c20b23/json-c-0.14-8.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/52a84086dc06dd09a9b8638199ba7fdfe36c49635174516a3f8bbe5634c20b23/json-c-0.14-8.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:25Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:25Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/json-c-0.14-8.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:25Z DEBUG lr_headercb: Server returned Content-Length: "123240" (converted 123240/123240 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/729fb71fb6a55415c48d8454d69229ffea3df5bb7263fa99a05b7285212271e5/gobject-introspection-1.82.0-1.amzn2023.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/729fb71fb6a55415c48d8454d69229ffea3df5bb7263fa99a05b7285212271e5/gobject-introspection-1.82.0-1.amzn2023.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 729fb71fb6a55415c48d8454d69229ffea3df5bb7263fa99a05b7285212271e5 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/6075dac99ce4a3e9d301505db13af89cd8456b20f59f0c7dc71b3e3f5b80c64e/keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/6075dac99ce4a3e9d301505db13af89cd8456b20f59f0c7dc71b3e3f5b80c64e/keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "40593" (converted 40593/40593 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/52a84086dc06dd09a9b8638199ba7fdfe36c49635174516a3f8bbe5634c20b23/json-c-0.14-8.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/52a84086dc06dd09a9b8638199ba7fdfe36c49635174516a3f8bbe5634c20b23/json-c-0.14-8.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 52a84086dc06dd09a9b8638199ba7fdfe36c49635174516a3f8bbe5634c20b23 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/a9c341b966b72b355cc91f59a8602a7fd0fec51d32943dda778c601900b5fdce/krb5-libs-1.21.3-1.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/a9c341b966b72b355cc91f59a8602a7fd0fec51d32943dda778c601900b5fdce/krb5-libs-1.21.3-1.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/krb5-libs-1.21.3-1.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "33274" (converted 33274/33274 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/6075dac99ce4a3e9d301505db13af89cd8456b20f59f0c7dc71b3e3f5b80c64e/keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/6075dac99ce4a3e9d301505db13af89cd8456b20f59f0c7dc71b3e3f5b80c64e/keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 6075dac99ce4a3e9d301505db13af89cd8456b20f59f0c7dc71b3e3f5b80c64e is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/e259da69c6475bf8fc1a4eef88a7edd05d76f94e9141419b8c00d9edb63c5547/libacl-2.3.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/e259da69c6475bf8fc1a4eef88a7edd05d76f94e9141419b8c00d9edb63c5547/libacl-2.3.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libacl-2.3.1-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "288273" (converted 288273/288273 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/0814a139bce5760c62d9f207c2b0209aecddebedbfc8c5bb645a11b70cab86f6/grep-3.8-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/0814a139bce5760c62d9f207c2b0209aecddebedbfc8c5bb645a11b70cab86f6/grep-3.8-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 0814a139bce5760c62d9f207c2b0209aecddebedbfc8c5bb645a11b70cab86f6 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/7fa6013f391984775e3e939e76a1f837e7ba68f9e00b0de2d8dee5ae09ab5b76/libarchive-3.7.4-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/7fa6013f391984775e3e939e76a1f837e7ba68f9e00b0de2d8dee5ae09ab5b76/libarchive-3.7.4-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libarchive-3.7.4-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "24716" (converted 24716/24716 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/e259da69c6475bf8fc1a4eef88a7edd05d76f94e9141419b8c00d9edb63c5547/libacl-2.3.1-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/e259da69c6475bf8fc1a4eef88a7edd05d76f94e9141419b8c00d9edb63c5547/libacl-2.3.1-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) e259da69c6475bf8fc1a4eef88a7edd05d76f94e9141419b8c00d9edb63c5547 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/c8fdc4842537be6e3eead012b2e0e4e3d9f78b10b1c8ef36a51206300fada7a8/libassuan-2.5.5-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/c8fdc4842537be6e3eead012b2e0e4e3d9f78b10b1c8ef36a51206300fada7a8/libassuan-2.5.5-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libassuan-2.5.5-1.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "785990" (converted 785990/785990 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/a9c341b966b72b355cc91f59a8602a7fd0fec51d32943dda778c601900b5fdce/krb5-libs-1.21.3-1.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/a9c341b966b72b355cc91f59a8602a7fd0fec51d32943dda778c601900b5fdce/krb5-libs-1.21.3-1.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) a9c341b966b72b355cc91f59a8602a7fd0fec51d32943dda778c601900b5fdce is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/8d932bb4f8760a16b8526821284078fbb6c272129f0cf806c591ebf3b6b26263/libattr-2.5.1-3.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/8d932bb4f8760a16b8526821284078fbb6c272129f0cf806c591ebf3b6b26263/libattr-2.5.1-3.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libattr-2.5.1-3.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "420490" (converted 420490/420490 expected)
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "68301" (converted 68301/68301 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/7fa6013f391984775e3e939e76a1f837e7ba68f9e00b0de2d8dee5ae09ab5b76/libarchive-3.7.4-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/7fa6013f391984775e3e939e76a1f837e7ba68f9e00b0de2d8dee5ae09ab5b76/libarchive-3.7.4-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 7fa6013f391984775e3e939e76a1f837e7ba68f9e00b0de2d8dee5ae09ab5b76 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/564a0b2f623ba289e8de6b6cf6156808c69a1866e3154a8efa41a6fe0a1c776a/libblkid-2.37.4-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/564a0b2f623ba289e8de6b6cf6156808c69a1866e3154a8efa41a6fe0a1c776a/libblkid-2.37.4-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libblkid-2.37.4-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/c8fdc4842537be6e3eead012b2e0e4e3d9f78b10b1c8ef36a51206300fada7a8/libassuan-2.5.5-1.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/c8fdc4842537be6e3eead012b2e0e4e3d9f78b10b1c8ef36a51206300fada7a8/libassuan-2.5.5-1.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) c8fdc4842537be6e3eead012b2e0e4e3d9f78b10b1c8ef36a51206300fada7a8 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/c6614dfdd952c1a9ca9e78bf2159d7a77e9bf6d14ceacf85e10e03e701e218f6/libcap-2.48-2.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/c6614dfdd952c1a9ca9e78bf2159d7a77e9bf6d14ceacf85e10e03e701e218f6/libcap-2.48-2.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libcap-2.48-2.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "19620" (converted 19620/19620 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/8d932bb4f8760a16b8526821284078fbb6c272129f0cf806c591ebf3b6b26263/libattr-2.5.1-3.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/8d932bb4f8760a16b8526821284078fbb6c272129f0cf806c591ebf3b6b26263/libattr-2.5.1-3.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 8d932bb4f8760a16b8526821284078fbb6c272129f0cf806c591ebf3b6b26263 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/8a64fba4e0664e827312edb312cc519ab78214b7ddb799d1a053f27108a9116a/libcap-ng-0.8.2-4.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/8a64fba4e0664e827312edb312cc519ab78214b7ddb799d1a053f27108a9116a/libcap-ng-0.8.2-4.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libcap-ng-0.8.2-4.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "107681" (converted 107681/107681 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/564a0b2f623ba289e8de6b6cf6156808c69a1866e3154a8efa41a6fe0a1c776a/libblkid-2.37.4-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/564a0b2f623ba289e8de6b6cf6156808c69a1866e3154a8efa41a6fe0a1c776a/libblkid-2.37.4-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 564a0b2f623ba289e8de6b6cf6156808c69a1866e3154a8efa41a6fe0a1c776a is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/7b0044d75eac628eb78a8185cef51e3b34dccd67fa69e8735ec7ddf9a770ff00/libcom_err-1.46.5-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/7b0044d75eac628eb78a8185cef51e3b34dccd67fa69e8735ec7ddf9a770ff00/libcom_err-1.46.5-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libcom_err-1.46.5-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "69263" (converted 69263/69263 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/c6614dfdd952c1a9ca9e78bf2159d7a77e9bf6d14ceacf85e10e03e701e218f6/libcap-2.48-2.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/c6614dfdd952c1a9ca9e78bf2159d7a77e9bf6d14ceacf85e10e03e701e218f6/libcap-2.48-2.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) c6614dfdd952c1a9ca9e78bf2159d7a77e9bf6d14ceacf85e10e03e701e218f6 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/a6658d92f67e9a027422c2a82f052fd8283ed87633f8e6ecd7db520a689f5c53/libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/a6658d92f67e9a027422c2a82f052fd8283ed87633f8e6ecd7db520a689f5c53/libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "33373" (converted 33373/33373 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/8a64fba4e0664e827312edb312cc519ab78214b7ddb799d1a053f27108a9116a/libcap-ng-0.8.2-4.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/8a64fba4e0664e827312edb312cc519ab78214b7ddb799d1a053f27108a9116a/libcap-ng-0.8.2-4.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 8a64fba4e0664e827312edb312cc519ab78214b7ddb799d1a053f27108a9116a is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/a4767eaed98fa3f71ea40e3758925aadf85db5a82b5241b89278c28003fa882c/libdnf-0.69.0-8.amzn2023.0.5.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/a4767eaed98fa3f71ea40e3758925aadf85db5a82b5241b89278c28003fa882c/libdnf-0.69.0-8.amzn2023.0.5.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libdnf-0.69.0-8.amzn2023.0.5.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "28012" (converted 28012/28012 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/7b0044d75eac628eb78a8185cef51e3b34dccd67fa69e8735ec7ddf9a770ff00/libcom_err-1.46.5-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/7b0044d75eac628eb78a8185cef51e3b34dccd67fa69e8735ec7ddf9a770ff00/libcom_err-1.46.5-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 7b0044d75eac628eb78a8185cef51e3b34dccd67fa69e8735ec7ddf9a770ff00 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/f4a8786ffc5b417a99db9bb55b4268bab7909cf3197aa8443ba8bb344bfbdaf2/libffi-3.4.4-1.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/f4a8786ffc5b417a99db9bb55b4268bab7909cf3197aa8443ba8bb344bfbdaf2/libffi-3.4.4-1.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libffi-3.4.4-1.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "277371" (converted 277371/277371 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/a6658d92f67e9a027422c2a82f052fd8283ed87633f8e6ecd7db520a689f5c53/libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/a6658d92f67e9a027422c2a82f052fd8283ed87633f8e6ecd7db520a689f5c53/libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) a6658d92f67e9a027422c2a82f052fd8283ed87633f8e6ecd7db520a689f5c53 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/9d60c83f19a3ee144bb15c201be4aca0f5a6a2e732d8f6da29f9dcd69d9b8f2d/libgcc-11.4.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/9d60c83f19a3ee144bb15c201be4aca0f5a6a2e732d8f6da29f9dcd69d9b8f2d/libgcc-11.4.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libgcc-11.4.1-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "38004" (converted 38004/38004 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/f4a8786ffc5b417a99db9bb55b4268bab7909cf3197aa8443ba8bb344bfbdaf2/libffi-3.4.4-1.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/f4a8786ffc5b417a99db9bb55b4268bab7909cf3197aa8443ba8bb344bfbdaf2/libffi-3.4.4-1.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) f4a8786ffc5b417a99db9bb55b4268bab7909cf3197aa8443ba8bb344bfbdaf2 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/e78957b2c808b1b1c6424c10ae9fb2c7efd997d8035eaadf42c118a3c10e6457/libgcrypt-1.10.2-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/e78957b2c808b1b1c6424c10ae9fb2c7efd997d8035eaadf42c118a3c10e6457/libgcrypt-1.10.2-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libgcrypt-1.10.2-1.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "620418" (converted 620418/620418 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/a4767eaed98fa3f71ea40e3758925aadf85db5a82b5241b89278c28003fa882c/libdnf-0.69.0-8.amzn2023.0.5.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/a4767eaed98fa3f71ea40e3758925aadf85db5a82b5241b89278c28003fa882c/libdnf-0.69.0-8.amzn2023.0.5.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) a4767eaed98fa3f71ea40e3758925aadf85db5a82b5241b89278c28003fa882c is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/77ae94cd73f999226cdf76c35fdeec0025e350d4d326ce644903318042c23713/libgpg-error-1.42-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/77ae94cd73f999226cdf76c35fdeec0025e350d4d326ce644903318042c23713/libgpg-error-1.42-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libgpg-error-1.42-1.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "492200" (converted 492200/492200 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/e78957b2c808b1b1c6424c10ae9fb2c7efd997d8035eaadf42c118a3c10e6457/libgcrypt-1.10.2-1.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/e78957b2c808b1b1c6424c10ae9fb2c7efd997d8035eaadf42c118a3c10e6457/libgcrypt-1.10.2-1.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) e78957b2c808b1b1c6424c10ae9fb2c7efd997d8035eaadf42c118a3c10e6457 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/ff67776790e736d3669ef87cfb1987f018751b024fb45e69717f4ec4e9f9eb22/libidn2-2.3.2-1.amzn2023.0.5.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/ff67776790e736d3669ef87cfb1987f018751b024fb45e69717f4ec4e9f9eb22/libidn2-2.3.2-1.amzn2023.0.5.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libidn2-2.3.2-1.amzn2023.0.5.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "88966" (converted 88966/88966 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/9d60c83f19a3ee144bb15c201be4aca0f5a6a2e732d8f6da29f9dcd69d9b8f2d/libgcc-11.4.1-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/9d60c83f19a3ee144bb15c201be4aca0f5a6a2e732d8f6da29f9dcd69d9b8f2d/libgcc-11.4.1-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 9d60c83f19a3ee144bb15c201be4aca0f5a6a2e732d8f6da29f9dcd69d9b8f2d is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/17299fcc88abf0f40fdd7de8841b267dd76c91d22307b64a433a0e20f64e7ba1/libmodulemd-2.13.0-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/17299fcc88abf0f40fdd7de8841b267dd76c91d22307b64a433a0e20f64e7ba1/libmodulemd-2.13.0-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libmodulemd-2.13.0-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "218167" (converted 218167/218167 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/77ae94cd73f999226cdf76c35fdeec0025e350d4d326ce644903318042c23713/libgpg-error-1.42-1.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/77ae94cd73f999226cdf76c35fdeec0025e350d4d326ce644903318042c23713/libgpg-error-1.42-1.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 77ae94cd73f999226cdf76c35fdeec0025e350d4d326ce644903318042c23713 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/32ac1afe36ec79eeb9d6af17142a4160cab24c8275c82e450c139d871ec0c951/libmount-2.37.4-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/32ac1afe36ec79eeb9d6af17142a4160cab24c8275c82e450c139d871ec0c951/libmount-2.37.4-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libmount-2.37.4-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "107159" (converted 107159/107159 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/ff67776790e736d3669ef87cfb1987f018751b024fb45e69717f4ec4e9f9eb22/libidn2-2.3.2-1.amzn2023.0.5.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/ff67776790e736d3669ef87cfb1987f018751b024fb45e69717f4ec4e9f9eb22/libidn2-2.3.2-1.amzn2023.0.5.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) ff67776790e736d3669ef87cfb1987f018751b024fb45e69717f4ec4e9f9eb22 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/9773158c5e26caa9c7ac413ca7576c7917a9cce34a55e633d16a9952221c0927/libnghttp2-1.59.0-3.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/9773158c5e26caa9c7ac413ca7576c7917a9cce34a55e633d16a9952221c0927/libnghttp2-1.59.0-3.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libnghttp2-1.59.0-3.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "211615" (converted 211615/211615 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/17299fcc88abf0f40fdd7de8841b267dd76c91d22307b64a433a0e20f64e7ba1/libmodulemd-2.13.0-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/17299fcc88abf0f40fdd7de8841b267dd76c91d22307b64a433a0e20f64e7ba1/libmodulemd-2.13.0-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 17299fcc88abf0f40fdd7de8841b267dd76c91d22307b64a433a0e20f64e7ba1 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/79d9e773316ae2228e40b77989bc3a2f49906d106d70a70fce7b6cd5526ea81e/libpeas-1.32.0-1.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/79d9e773316ae2228e40b77989bc3a2f49906d106d70a70fce7b6cd5526ea81e/libpeas-1.32.0-1.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libpeas-1.32.0-1.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "132805" (converted 132805/132805 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/32ac1afe36ec79eeb9d6af17142a4160cab24c8275c82e450c139d871ec0c951/libmount-2.37.4-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/32ac1afe36ec79eeb9d6af17142a4160cab24c8275c82e450c139d871ec0c951/libmount-2.37.4-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 32ac1afe36ec79eeb9d6af17142a4160cab24c8275c82e450c139d871ec0c951 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/a851c067adbf9a57bc282ea3a9c470bc82e32b4215eea2a4bc5106ae40b1d748/libpsl-0.21.1-3.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/a851c067adbf9a57bc282ea3a9c470bc82e32b4215eea2a4bc5106ae40b1d748/libpsl-0.21.1-3.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libpsl-0.21.1-3.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "121751" (converted 121751/121751 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/79d9e773316ae2228e40b77989bc3a2f49906d106d70a70fce7b6cd5526ea81e/libpeas-1.32.0-1.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/79d9e773316ae2228e40b77989bc3a2f49906d106d70a70fce7b6cd5526ea81e/libpeas-1.32.0-1.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 79d9e773316ae2228e40b77989bc3a2f49906d106d70a70fce7b6cd5526ea81e is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/3f6a5587ca21a65ab7c30e31a3bfa5772df61ff2938ceaaf93b312d011699fc9/librepo-1.14.5-2.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/3f6a5587ca21a65ab7c30e31a3bfa5772df61ff2938ceaaf93b312d011699fc9/librepo-1.14.5-2.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/librepo-1.14.5-2.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "79652" (converted 79652/79652 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/9773158c5e26caa9c7ac413ca7576c7917a9cce34a55e633d16a9952221c0927/libnghttp2-1.59.0-3.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/9773158c5e26caa9c7ac413ca7576c7917a9cce34a55e633d16a9952221c0927/libnghttp2-1.59.0-3.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 9773158c5e26caa9c7ac413ca7576c7917a9cce34a55e633d16a9952221c0927 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/d93a5c5d224a31d019187e37a971f9e7f6dcd1225080bffa7187bda69e3907de/libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d93a5c5d224a31d019187e37a971f9e7f6dcd1225080bffa7187bda69e3907de/libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "62676" (converted 62676/62676 expected)
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "89150" (converted 89150/89150 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/a851c067adbf9a57bc282ea3a9c470bc82e32b4215eea2a4bc5106ae40b1d748/libpsl-0.21.1-3.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/a851c067adbf9a57bc282ea3a9c470bc82e32b4215eea2a4bc5106ae40b1d748/libpsl-0.21.1-3.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) a851c067adbf9a57bc282ea3a9c470bc82e32b4215eea2a4bc5106ae40b1d748 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/3f6d54aef848223d05ca20ebd55c9d6416d708d8b2578de7717f00fbb664d27e/libselinux-3.4-5.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/3f6d54aef848223d05ca20ebd55c9d6416d708d8b2578de7717f00fbb664d27e/libselinux-3.4-5.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libselinux-3.4-5.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/3f6a5587ca21a65ab7c30e31a3bfa5772df61ff2938ceaaf93b312d011699fc9/librepo-1.14.5-2.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/3f6a5587ca21a65ab7c30e31a3bfa5772df61ff2938ceaaf93b312d011699fc9/librepo-1.14.5-2.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 3f6a5587ca21a65ab7c30e31a3bfa5772df61ff2938ceaaf93b312d011699fc9 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/d24d097da83144de4bc48e649e32ecd7b61c31ef2d9b8b88146bdaaf8e574b95/libsepol-3.4-3.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d24d097da83144de4bc48e649e32ecd7b61c31ef2d9b8b88146bdaaf8e574b95/libsepol-3.4-3.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libsepol-3.4-3.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "10519" (converted 10519/10519 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/d93a5c5d224a31d019187e37a971f9e7f6dcd1225080bffa7187bda69e3907de/libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/d93a5c5d224a31d019187e37a971f9e7f6dcd1225080bffa7187bda69e3907de/libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) d93a5c5d224a31d019187e37a971f9e7f6dcd1225080bffa7187bda69e3907de is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/15742c87d59656ef05a91999871d239a8fb5452f81165e22426e120f932b333e/libsigsegv-2.13-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/15742c87d59656ef05a91999871d239a8fb5452f81165e22426e120f932b333e/libsigsegv-2.13-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libsigsegv-2.13-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "89224" (converted 89224/89224 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/3f6d54aef848223d05ca20ebd55c9d6416d708d8b2578de7717f00fbb664d27e/libselinux-3.4-5.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/3f6d54aef848223d05ca20ebd55c9d6416d708d8b2578de7717f00fbb664d27e/libselinux-3.4-5.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 3f6d54aef848223d05ca20ebd55c9d6416d708d8b2578de7717f00fbb664d27e is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/6fc160eb5a7de838f2f7efc78fc191bdc7623091ca67ed052a28081da97beec6/libsmartcols-2.37.4-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/6fc160eb5a7de838f2f7efc78fc191bdc7623091ca67ed052a28081da97beec6/libsmartcols-2.37.4-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libsmartcols-2.37.4-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "318102" (converted 318102/318102 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/d24d097da83144de4bc48e649e32ecd7b61c31ef2d9b8b88146bdaaf8e574b95/libsepol-3.4-3.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/d24d097da83144de4bc48e649e32ecd7b61c31ef2d9b8b88146bdaaf8e574b95/libsepol-3.4-3.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) d24d097da83144de4bc48e649e32ecd7b61c31ef2d9b8b88146bdaaf8e574b95 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/3fc85db75835b774cc04137e92775ceb3f44ccdb43d54dd2f7b3ed30b2cdc1f0/libsolv-0.7.22-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/3fc85db75835b774cc04137e92775ceb3f44ccdb43d54dd2f7b3ed30b2cdc1f0/libsolv-0.7.22-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libsolv-0.7.22-1.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "61707" (converted 61707/61707 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/6fc160eb5a7de838f2f7efc78fc191bdc7623091ca67ed052a28081da97beec6/libsmartcols-2.37.4-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/6fc160eb5a7de838f2f7efc78fc191bdc7623091ca67ed052a28081da97beec6/libsmartcols-2.37.4-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 6fc160eb5a7de838f2f7efc78fc191bdc7623091ca67ed052a28081da97beec6 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/79190d8096474765b33d21369f1f3ba72613d97fbd7779ee8f3afe1b96fe4b3b/libstdc%2b%2b-11.4.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/79190d8096474765b33d21369f1f3ba72613d97fbd7779ee8f3afe1b96fe4b3b/libstdc%2b%2b-11.4.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libstdc++-11.4.1-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "28329" (converted 28329/28329 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/15742c87d59656ef05a91999871d239a8fb5452f81165e22426e120f932b333e/libsigsegv-2.13-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/15742c87d59656ef05a91999871d239a8fb5452f81165e22426e120f932b333e/libsigsegv-2.13-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 15742c87d59656ef05a91999871d239a8fb5452f81165e22426e120f932b333e is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/ed01d6bc4d5a75dcef9f983b771051eecf20a08519d78517c6668c3278a147fb/libtasn1-4.19.0-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/ed01d6bc4d5a75dcef9f983b771051eecf20a08519d78517c6668c3278a147fb/libtasn1-4.19.0-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libtasn1-4.19.0-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "387730" (converted 387730/387730 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/3fc85db75835b774cc04137e92775ceb3f44ccdb43d54dd2f7b3ed30b2cdc1f0/libsolv-0.7.22-1.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/3fc85db75835b774cc04137e92775ceb3f44ccdb43d54dd2f7b3ed30b2cdc1f0/libsolv-0.7.22-1.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 3fc85db75835b774cc04137e92775ceb3f44ccdb43d54dd2f7b3ed30b2cdc1f0 is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/db7d52a4bbccbf0ce7febf6018b25403e617dfc7060ac9f609544848100ddbfd/libunistring-0.9.10-10.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/db7d52a4bbccbf0ce7febf6018b25403e617dfc7060ac9f609544848100ddbfd/libunistring-0.9.10-10.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libunistring-0.9.10-10.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "707686" (converted 707686/707686 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/79190d8096474765b33d21369f1f3ba72613d97fbd7779ee8f3afe1b96fe4b3b/libstdc%2b%2b-11.4.1-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/79190d8096474765b33d21369f1f3ba72613d97fbd7779ee8f3afe1b96fe4b3b/libstdc%2b%2b-11.4.1-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 79190d8096474765b33d21369f1f3ba72613d97fbd7779ee8f3afe1b96fe4b3b is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/e768518d278e30b71d9e52f7eb72577e948f701b75d8f837db4a9718eb8d975a/libuuid-2.37.4-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/e768518d278e30b71d9e52f7eb72577e948f701b75d8f837db4a9718eb8d975a/libuuid-2.37.4-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libuuid-2.37.4-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "505227" (converted 505227/505227 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/db7d52a4bbccbf0ce7febf6018b25403e617dfc7060ac9f609544848100ddbfd/libunistring-0.9.10-10.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/db7d52a4bbccbf0ce7febf6018b25403e617dfc7060ac9f609544848100ddbfd/libunistring-0.9.10-10.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) db7d52a4bbccbf0ce7febf6018b25403e617dfc7060ac9f609544848100ddbfd is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/66a901c5895106101303eecb2c874d2615453357af767bfe4e6ee056bb1b4f7f/libverto-0.3.2-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/66a901c5895106101303eecb2c874d2615453357af767bfe4e6ee056bb1b4f7f/libverto-0.3.2-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libverto-0.3.2-1.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "76956" (converted 76956/76956 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/ed01d6bc4d5a75dcef9f983b771051eecf20a08519d78517c6668c3278a147fb/libtasn1-4.19.0-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/ed01d6bc4d5a75dcef9f983b771051eecf20a08519d78517c6668c3278a147fb/libtasn1-4.19.0-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) ed01d6bc4d5a75dcef9f983b771051eecf20a08519d78517c6668c3278a147fb is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/c411c2ed7dc7469d2c84d90f75953e1f00a659951fed0f82adee895ac23bf89c/libxml2-2.10.4-1.amzn2023.0.7.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/c411c2ed7dc7469d2c84d90f75953e1f00a659951fed0f82adee895ac23bf89c/libxml2-2.10.4-1.amzn2023.0.7.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libxml2-2.10.4-1.amzn2023.0.7.aarch64.rpm): Operation not supported
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "22045" (converted 22045/22045 expected)
2025-01-07T22:24:26Z DEBUG lr_headercb: Server returned Content-Length: "26837" (converted 26837/26837 expected)
2025-01-07T22:24:26Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/e768518d278e30b71d9e52f7eb72577e948f701b75d8f837db4a9718eb8d975a/libuuid-2.37.4-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/e768518d278e30b71d9e52f7eb72577e948f701b75d8f837db4a9718eb8d975a/libuuid-2.37.4-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:26Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) e768518d278e30b71d9e52f7eb72577e948f701b75d8f837db4a9718eb8d975a is OK
2025-01-07T22:24:26Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/90665839c478bbbbd6e2e8eb3cf1484a1d95f6cd66dc739a049777aa52f358b8/libyaml-0.2.5-5.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/90665839c478bbbbd6e2e8eb3cf1484a1d95f6cd66dc739a049777aa52f358b8/libyaml-0.2.5-5.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:26Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:26Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libyaml-0.2.5-5.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/66a901c5895106101303eecb2c874d2615453357af767bfe4e6ee056bb1b4f7f/libverto-0.3.2-1.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/66a901c5895106101303eecb2c874d2615453357af767bfe4e6ee056bb1b4f7f/libverto-0.3.2-1.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 66a901c5895106101303eecb2c874d2615453357af767bfe4e6ee056bb1b4f7f is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/7df4dbe167421445491e7bf93a8be9caea1800c3a382ccb4da60b18ad542ca6c/libzstd-1.5.5-1.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/7df4dbe167421445491e7bf93a8be9caea1800c3a382ccb4da60b18ad542ca6c/libzstd-1.5.5-1.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libzstd-1.5.5-1.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "61662" (converted 61662/61662 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/90665839c478bbbbd6e2e8eb3cf1484a1d95f6cd66dc739a049777aa52f358b8/libyaml-0.2.5-5.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/90665839c478bbbbd6e2e8eb3cf1484a1d95f6cd66dc739a049777aa52f358b8/libyaml-0.2.5-5.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 90665839c478bbbbd6e2e8eb3cf1484a1d95f6cd66dc739a049777aa52f358b8 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/d16303135f65cdeb1291691bba5f4b74c68015ac0418a808ad1385799298401b/lua-libs-5.4.4-3.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d16303135f65cdeb1291691bba5f4b74c68015ac0418a808ad1385799298401b/lua-libs-5.4.4-3.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/lua-libs-5.4.4-3.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "706100" (converted 706100/706100 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/c411c2ed7dc7469d2c84d90f75953e1f00a659951fed0f82adee895ac23bf89c/libxml2-2.10.4-1.amzn2023.0.7.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/c411c2ed7dc7469d2c84d90f75953e1f00a659951fed0f82adee895ac23bf89c/libxml2-2.10.4-1.amzn2023.0.7.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) c411c2ed7dc7469d2c84d90f75953e1f00a659951fed0f82adee895ac23bf89c is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/9a2ffcf61798358c0d59de3177d6b44bfd31702eaf00d19b4ddec2ba4228b3ff/lz4-libs-1.9.4-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/9a2ffcf61798358c0d59de3177d6b44bfd31702eaf00d19b4ddec2ba4228b3ff/lz4-libs-1.9.4-1.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/lz4-libs-1.9.4-1.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "291021" (converted 291021/291021 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/7df4dbe167421445491e7bf93a8be9caea1800c3a382ccb4da60b18ad542ca6c/libzstd-1.5.5-1.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/7df4dbe167421445491e7bf93a8be9caea1800c3a382ccb4da60b18ad542ca6c/libzstd-1.5.5-1.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 7df4dbe167421445491e7bf93a8be9caea1800c3a382ccb4da60b18ad542ca6c is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/6bcbbf424df9489e25122b7c4e871e6c8a40241cd764cf1d13435100a0689b5b/microdnf-3.10.0-2.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/6bcbbf424df9489e25122b7c4e871e6c8a40241cd764cf1d13435100a0689b5b/microdnf-3.10.0-2.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/microdnf-3.10.0-2.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "219487" (converted 219487/219487 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/d16303135f65cdeb1291691bba5f4b74c68015ac0418a808ad1385799298401b/lua-libs-5.4.4-3.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/d16303135f65cdeb1291691bba5f4b74c68015ac0418a808ad1385799298401b/lua-libs-5.4.4-3.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) d16303135f65cdeb1291691bba5f4b74c68015ac0418a808ad1385799298401b is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/2a0c4b17c3c3060a292c056ac111b204b8f77572be737a2c637f3249ee133c64/microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/2a0c4b17c3c3060a292c056ac111b204b8f77572be737a2c637f3249ee133c64/microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "52608" (converted 52608/52608 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/6bcbbf424df9489e25122b7c4e871e6c8a40241cd764cf1d13435100a0689b5b/microdnf-3.10.0-2.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/6bcbbf424df9489e25122b7c4e871e6c8a40241cd764cf1d13435100a0689b5b/microdnf-3.10.0-2.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 6bcbbf424df9489e25122b7c4e871e6c8a40241cd764cf1d13435100a0689b5b is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/3162b14361101503c33b40ecda4bc766fbd97635e5f671c79699c22c2a735af6/mpfr-4.1.0-7.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/3162b14361101503c33b40ecda4bc766fbd97635e5f671c79699c22c2a735af6/mpfr-4.1.0-7.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/mpfr-4.1.0-7.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "74373" (converted 74373/74373 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/9a2ffcf61798358c0d59de3177d6b44bfd31702eaf00d19b4ddec2ba4228b3ff/lz4-libs-1.9.4-1.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/9a2ffcf61798358c0d59de3177d6b44bfd31702eaf00d19b4ddec2ba4228b3ff/lz4-libs-1.9.4-1.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 9a2ffcf61798358c0d59de3177d6b44bfd31702eaf00d19b4ddec2ba4228b3ff is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/cf22ad6a84e56f5172a4b28ead36c11274adf48a9b9f080fd4e5154444737ea0/ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/cf22ad6a84e56f5172a4b28ead36c11274adf48a9b9f080fd4e5154444737ea0/ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "9578" (converted 9578/9578 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/2a0c4b17c3c3060a292c056ac111b204b8f77572be737a2c637f3249ee133c64/microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/2a0c4b17c3c3060a292c056ac111b204b8f77572be737a2c637f3249ee133c64/microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 2a0c4b17c3c3060a292c056ac111b204b8f77572be737a2c637f3249ee133c64 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/7bd0fba806b5c1408e865166f8f8b98c7c0695105dbb58e41fd5b4759a38b96f/ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/7bd0fba806b5c1408e865166f8f8b98c7c0695105dbb58e41fd5b4759a38b96f/ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "248967" (converted 248967/248967 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/3162b14361101503c33b40ecda4bc766fbd97635e5f671c79699c22c2a735af6/mpfr-4.1.0-7.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/3162b14361101503c33b40ecda4bc766fbd97635e5f671c79699c22c2a735af6/mpfr-4.1.0-7.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 3162b14361101503c33b40ecda4bc766fbd97635e5f671c79699c22c2a735af6 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/15918f7b5853a125fbcb31fb6ff80c1a24e0604e4cd5806458d5ef31f5a90d18/npth-1.6-6.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/15918f7b5853a125fbcb31fb6ff80c1a24e0604e4cd5806458d5ef31f5a90d18/npth-1.6-6.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/npth-1.6-6.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "61672" (converted 61672/61672 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/cf22ad6a84e56f5172a4b28ead36c11274adf48a9b9f080fd4e5154444737ea0/ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/cf22ad6a84e56f5172a4b28ead36c11274adf48a9b9f080fd4e5154444737ea0/ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) cf22ad6a84e56f5172a4b28ead36c11274adf48a9b9f080fd4e5154444737ea0 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/d2b8f944c88cfb8804ebe2e4b4e4d32e108ef4573e1197df1ba5e3b75737e70b/openssl-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d2b8f944c88cfb8804ebe2e4b4e4d32e108ef4573e1197df1ba5e3b75737e70b/openssl-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/openssl-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "25783" (converted 25783/25783 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/15918f7b5853a125fbcb31fb6ff80c1a24e0604e4cd5806458d5ef31f5a90d18/npth-1.6-6.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/15918f7b5853a125fbcb31fb6ff80c1a24e0604e4cd5806458d5ef31f5a90d18/npth-1.6-6.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 15918f7b5853a125fbcb31fb6ff80c1a24e0604e4cd5806458d5ef31f5a90d18 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/ee0c06ab41a43f9b77ddc905ae266093cb3afcad9ca681740e8f483372891c91/p11-kit-0.24.1-2.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/ee0c06ab41a43f9b77ddc905ae266093cb3afcad9ca681740e8f483372891c91/p11-kit-0.24.1-2.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/p11-kit-0.24.1-2.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "325761" (converted 325761/325761 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/7bd0fba806b5c1408e865166f8f8b98c7c0695105dbb58e41fd5b4759a38b96f/ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/7bd0fba806b5c1408e865166f8f8b98c7c0695105dbb58e41fd5b4759a38b96f/ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 7bd0fba806b5c1408e865166f8f8b98c7c0695105dbb58e41fd5b4759a38b96f is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/0ce1183338177106f0d5ec41ffb9928d2220d5c758a482aa286eeb6b0356b0b4/p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/0ce1183338177106f0d5ec41ffb9928d2220d5c758a482aa286eeb6b0356b0b4/p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "2158381" (converted 2158381/2158381 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/d2b8f944c88cfb8804ebe2e4b4e4d32e108ef4573e1197df1ba5e3b75737e70b/openssl-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/d2b8f944c88cfb8804ebe2e4b4e4d32e108ef4573e1197df1ba5e3b75737e70b/openssl-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) d2b8f944c88cfb8804ebe2e4b4e4d32e108ef4573e1197df1ba5e3b75737e70b is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/54796fd3055837baaadc9156b2135d4e07d102fc1c85a400d9cf13245a3e7871/pcre2-10.40-1.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/54796fd3055837baaadc9156b2135d4e07d102fc1c85a400d9cf13245a3e7871/pcre2-10.40-1.amzn2023.0.3.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/pcre2-10.40-1.amzn2023.0.3.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "354010" (converted 354010/354010 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/ee0c06ab41a43f9b77ddc905ae266093cb3afcad9ca681740e8f483372891c91/p11-kit-0.24.1-2.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/ee0c06ab41a43f9b77ddc905ae266093cb3afcad9ca681740e8f483372891c91/p11-kit-0.24.1-2.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) ee0c06ab41a43f9b77ddc905ae266093cb3afcad9ca681740e8f483372891c91 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/1ab189dde1bf9f401f32498a8a51b22a85a2963e6bf4a4dcedb25adb8bb48529/pcre2-syntax-10.40-1.amzn2023.0.3.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/1ab189dde1bf9f401f32498a8a51b22a85a2963e6bf4a4dcedb25adb8bb48529/pcre2-syntax-10.40-1.amzn2023.0.3.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/pcre2-syntax-10.40-1.amzn2023.0.3.noarch.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "141329" (converted 141329/141329 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/0ce1183338177106f0d5ec41ffb9928d2220d5c758a482aa286eeb6b0356b0b4/p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/0ce1183338177106f0d5ec41ffb9928d2220d5c758a482aa286eeb6b0356b0b4/p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 0ce1183338177106f0d5ec41ffb9928d2220d5c758a482aa286eeb6b0356b0b4 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/131a82482a33aa8c0351161f72351dfaf994bf501235c662bdc40ae53658ea8b/popt-1.18-6.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/131a82482a33aa8c0351161f72351dfaf994bf501235c662bdc40ae53658ea8b/popt-1.18-6.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/popt-1.18-6.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "144904" (converted 144904/144904 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/1ab189dde1bf9f401f32498a8a51b22a85a2963e6bf4a4dcedb25adb8bb48529/pcre2-syntax-10.40-1.amzn2023.0.3.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/1ab189dde1bf9f401f32498a8a51b22a85a2963e6bf4a4dcedb25adb8bb48529/pcre2-syntax-10.40-1.amzn2023.0.3.noarch.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 1ab189dde1bf9f401f32498a8a51b22a85a2963e6bf4a4dcedb25adb8bb48529 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/fe51b2668e626bf16dcc9bb8f17c6825e5ca59839466e49bca72d5940f690b1e/publicsuffix-list-dafsa-20240212-61.amzn2023.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/fe51b2668e626bf16dcc9bb8f17c6825e5ca59839466e49bca72d5940f690b1e/publicsuffix-list-dafsa-20240212-61.amzn2023.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/publicsuffix-list-dafsa-20240212-61.amzn2023.noarch.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "225138" (converted 225138/225138 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/54796fd3055837baaadc9156b2135d4e07d102fc1c85a400d9cf13245a3e7871/pcre2-10.40-1.amzn2023.0.3.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/54796fd3055837baaadc9156b2135d4e07d102fc1c85a400d9cf13245a3e7871/pcre2-10.40-1.amzn2023.0.3.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 54796fd3055837baaadc9156b2135d4e07d102fc1c85a400d9cf13245a3e7871 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/b17aa6231896adb077ec1ca1dd8ae19168fdfee4b2b156998e163eef45bfc48b/readline-8.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/b17aa6231896adb077ec1ca1dd8ae19168fdfee4b2b156998e163eef45bfc48b/readline-8.1-2.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/readline-8.1-2.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "60834" (converted 60834/60834 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/fe51b2668e626bf16dcc9bb8f17c6825e5ca59839466e49bca72d5940f690b1e/publicsuffix-list-dafsa-20240212-61.amzn2023.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/fe51b2668e626bf16dcc9bb8f17c6825e5ca59839466e49bca72d5940f690b1e/publicsuffix-list-dafsa-20240212-61.amzn2023.noarch.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) fe51b2668e626bf16dcc9bb8f17c6825e5ca59839466e49bca72d5940f690b1e is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/09468e67b6afdff8d25ea577efa589a1810e15d35b89197fa9c3e2f8eb09fd55/rpm-4.16.1.3-29.amzn2023.0.6.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/09468e67b6afdff8d25ea577efa589a1810e15d35b89197fa9c3e2f8eb09fd55/rpm-4.16.1.3-29.amzn2023.0.6.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/rpm-4.16.1.3-29.amzn2023.0.6.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "60883" (converted 60883/60883 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/131a82482a33aa8c0351161f72351dfaf994bf501235c662bdc40ae53658ea8b/popt-1.18-6.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/131a82482a33aa8c0351161f72351dfaf994bf501235c662bdc40ae53658ea8b/popt-1.18-6.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 131a82482a33aa8c0351161f72351dfaf994bf501235c662bdc40ae53658ea8b is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/25ca74315525e917f8636a29779d49b2be394b16f51985eb31f05aacd1a8647b/rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/25ca74315525e917f8636a29779d49b2be394b16f51985eb31f05aacd1a8647b/rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "216915" (converted 216915/216915 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/b17aa6231896adb077ec1ca1dd8ae19168fdfee4b2b156998e163eef45bfc48b/readline-8.1-2.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/b17aa6231896adb077ec1ca1dd8ae19168fdfee4b2b156998e163eef45bfc48b/readline-8.1-2.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) b17aa6231896adb077ec1ca1dd8ae19168fdfee4b2b156998e163eef45bfc48b is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/ec9ced376b62d2c3682ac2b5edbae9c4312617d8b2f37b386d2e8de4de0f04e1/sed-4.8-7.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/ec9ced376b62d2c3682ac2b5edbae9c4312617d8b2f37b386d2e8de4de0f04e1/sed-4.8-7.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/sed-4.8-7.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "497007" (converted 497007/497007 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/09468e67b6afdff8d25ea577efa589a1810e15d35b89197fa9c3e2f8eb09fd55/rpm-4.16.1.3-29.amzn2023.0.6.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/09468e67b6afdff8d25ea577efa589a1810e15d35b89197fa9c3e2f8eb09fd55/rpm-4.16.1.3-29.amzn2023.0.6.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 09468e67b6afdff8d25ea577efa589a1810e15d35b89197fa9c3e2f8eb09fd55 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/32505f07050b1e1f76a5ed0eec0af03f773d27ccf6f6c01e8a7d32c583b5593f/setup-2.13.7-3.amzn2023.0.2.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/32505f07050b1e1f76a5ed0eec0af03f773d27ccf6f6c01e8a7d32c583b5593f/setup-2.13.7-3.amzn2023.0.2.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/setup-2.13.7-3.amzn2023.0.2.noarch.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "301962" (converted 301962/301962 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/ec9ced376b62d2c3682ac2b5edbae9c4312617d8b2f37b386d2e8de4de0f04e1/sed-4.8-7.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/ec9ced376b62d2c3682ac2b5edbae9c4312617d8b2f37b386d2e8de4de0f04e1/sed-4.8-7.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) ec9ced376b62d2c3682ac2b5edbae9c4312617d8b2f37b386d2e8de4de0f04e1 is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/06990a4e4d12bd014cc064846d1f8ee69f2d885d70949baeee6ca75d4050c233/sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/06990a4e4d12bd014cc064846d1f8ee69f2d885d70949baeee6ca75d4050c233/sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "315150" (converted 315150/315150 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/25ca74315525e917f8636a29779d49b2be394b16f51985eb31f05aacd1a8647b/rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/25ca74315525e917f8636a29779d49b2be394b16f51985eb31f05aacd1a8647b/rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 25ca74315525e917f8636a29779d49b2be394b16f51985eb31f05aacd1a8647b is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/0c4aab2f11620d58c3520b50d8c58fefe6beea597fa14227bf07393223380b7e/system-release-2023.6.20250107-0.amzn2023.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/0c4aab2f11620d58c3520b50d8c58fefe6beea597fa14227bf07393223380b7e/system-release-2023.6.20250107-0.amzn2023.noarch.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/system-release-2023.6.20250107-0.amzn2023.noarch.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "144557" (converted 144557/144557 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/32505f07050b1e1f76a5ed0eec0af03f773d27ccf6f6c01e8a7d32c583b5593f/setup-2.13.7-3.amzn2023.0.2.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/32505f07050b1e1f76a5ed0eec0af03f773d27ccf6f6c01e8a7d32c583b5593f/setup-2.13.7-3.amzn2023.0.2.noarch.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 32505f07050b1e1f76a5ed0eec0af03f773d27ccf6f6c01e8a7d32c583b5593f is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/a89ed2c87b25bc505ca2125606bfc61e8802729c41a5c074add0905fb750b126/xz-libs-5.2.5-9.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/a89ed2c87b25bc505ca2125606bfc61e8802729c41a5c074add0905fb750b126/xz-libs-5.2.5-9.amzn2023.0.2.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/xz-libs-5.2.5-9.amzn2023.0.2.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "20751" (converted 20751/20751 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/0c4aab2f11620d58c3520b50d8c58fefe6beea597fa14227bf07393223380b7e/system-release-2023.6.20250107-0.amzn2023.noarch.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/0c4aab2f11620d58c3520b50d8c58fefe6beea597fa14227bf07393223380b7e/system-release-2023.6.20250107-0.amzn2023.noarch.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 0c4aab2f11620d58c3520b50d8c58fefe6beea597fa14227bf07393223380b7e is OK
2025-01-07T22:24:27Z DEBUG select_next_target: Selecting mirror for: ../../../../blobstore/cd43f0c4767609d6d231d27613da70672ee8a444a9ebf03d766e77fa536131f7/zlib-1.2.11-33.amzn2023.0.5.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: URL: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/cd43f0c4767609d6d231d27613da70672ee8a444a9ebf03d766e77fa536131f7/zlib-1.2.11-33.amzn2023.0.5.aarch64.rpm
2025-01-07T22:24:27Z DEBUG prepare_next_transfer: Resume ignored, existing file was not originally being downloaded by Librepo
2025-01-07T22:24:27Z DEBUG add_librepo_xattr: Cannot set xattr user.Librepo.DownloadInProgress (/tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/zlib-1.2.11-33.amzn2023.0.5.aarch64.rpm): Operation not supported
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "675993" (converted 675993/675993 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/06990a4e4d12bd014cc064846d1f8ee69f2d885d70949baeee6ca75d4050c233/sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/06990a4e4d12bd014cc064846d1f8ee69f2d885d70949baeee6ca75d4050c233/sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) 06990a4e4d12bd014cc064846d1f8ee69f2d885d70949baeee6ca75d4050c233 is OK
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "93731" (converted 93731/93731 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/a89ed2c87b25bc505ca2125606bfc61e8802729c41a5c074add0905fb750b126/xz-libs-5.2.5-9.amzn2023.0.2.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/a89ed2c87b25bc505ca2125606bfc61e8802729c41a5c074add0905fb750b126/xz-libs-5.2.5-9.amzn2023.0.2.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) a89ed2c87b25bc505ca2125606bfc61e8802729c41a5c074add0905fb750b126 is OK
2025-01-07T22:24:27Z DEBUG lr_headercb: Server returned Content-Length: "93625" (converted 93625/93625 expected)
2025-01-07T22:24:27Z DEBUG check_transfer_statuses: Transfer finished: ../../../../blobstore/cd43f0c4767609d6d231d27613da70672ee8a444a9ebf03d766e77fa536131f7/zlib-1.2.11-33.amzn2023.0.5.aarch64.rpm (Effective url: https://al2023-repos-us-west-2-de612dc2.s3.dualstack.us-west-2.amazonaws.com/blobstore/cd43f0c4767609d6d231d27613da70672ee8a444a9ebf03d766e77fa536131f7/zlib-1.2.11-33.amzn2023.0.5.aarch64.rpm)
2025-01-07T22:24:27Z DEBUG check_finished_trasfer_checksum: Checksum (sha256) cd43f0c4767609d6d231d27613da70672ee8a444a9ebf03d766e77fa536131f7 is OK
2025-01-07T22:24:27Z DEBUG lr_download_packages: Restoring an old SIGINT handler
2025-01-07T22:24:32Z DEBUG Librepo version: 1.8.0 with CURL_GLOBAL_ACK_EINTR support (libcurl/8.3.0 OpenSSL/1.0.2k-fips zlib/1.2.7 libidn2/2.3.0 libpsl/0.21.5 (+libidn2/2.3.0) libssh2/1.4.3 nghttp2/1.41.0 OpenLDAP/2.4.44)
2025-01-07T22:24:32Z DEBUG Current date: 2025-01-07T22:24:32+0000
2025-01-13T14:44:38+0000 INFO Librepo version: 1.14.5 with CURL_GLOBAL_ACK_EINTR support (libcurl/8.5.0 OpenSSL/3.0.8 zlib/1.2.11 libidn2/2.3.2 libpsl/0.21.1 (+libidn2/2.3.2) nghttp2/1.59.0)
2025-01-13T14:44:38+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/mirrors/2023.6.20250107/aarch64/mirror.list
2025-01-13T14:44:38+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/repomd.xml
2025-01-13T14:44:38+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/primary.xml.gz
2025-01-13T14:44:38+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/filelists.xml.gz
2025-01-13T14:44:38+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/updateinfo.xml.gz
2025-01-13T14:44:38+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/repodata/comps.xml.gz
2025-01-13T14:44:47+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/mirrors/2023.6.20250107/aarch64/mirror.list
2025-01-13T14:44:47+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/c3c3ff13cfe3f93377c17001b6415daf5f02f7c793d1471171d5f0cbdb7d90ba/tzdata-2024a-1.amzn2023.0.1.noarch.rpm
2025-01-13T14:44:47+0000 INFO Librepo version: 1.14.5 with CURL_GLOBAL_ACK_EINTR support (libcurl/8.5.0 OpenSSL/3.0.8 zlib/1.2.11 libidn2/2.3.2 libpsl/0.21.1 (+libidn2/2.3.2) nghttp2/1.59.0)
2025-01-13T14:44:48+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/mirrors/2023.6.20250107/aarch64/mirror.list
2025-01-13T14:44:48+0000 INFO Downloading: https://cdn.amazonlinux.com/al2023/core/guids/5c087bdfa880b97f47c293e9851f399d3a614747545ba1a957f35e678da195f2/aarch64/../../../../blobstore/d17acffc1dd6012fa90746e80dc88b55e8f46cb054923cca245bd6f0d4ddce8f/openssl-snapsafe-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm
2025-01-13T14:45:01+0000 INFO Librepo version: 1.14.5 with CURL_GLOBAL_ACK_EINTR support (libcurl/8.5.0 OpenSSL/3.0.8 zlib/1.2.11 libidn2/2.3.2 libpsl/0.21.1 (+libidn2/2.3.2) nghttp2/1.59.0)
2025-01-13T14:45:22+0000 INFO Librepo version: 1.14.5 with CURL_GLOBAL_ACK_EINTR support (libcurl/8.5.0 OpenSSL/3.0.8 zlib/1.2.11 libidn2/2.3.2 libpsl/0.21.1 (+libidn2/2.3.2) nghttp2/1.59.0)
