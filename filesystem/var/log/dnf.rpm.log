2025-01-07T22:24:14Z INFO --- logging initialized ---
2025-01-07T22:24:29Z SUBDEBUG Installed: libgcc-11.4.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libgcc-11.4.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: publicsuffix-list-dafsa-20240212-61.amzn2023.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: publicsuffix-list-dafsa-20240212-61.amzn2023.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: pcre2-syntax-10.40-1.amzn2023.0.3.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: pcre2-syntax-10.40-1.amzn2023.0.3.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: bash-5.2.15-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: bash-5.2.15-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: system-release-2023.6.20250107-0.amzn2023.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: system-release-2023.6.20250107-0.amzn2023.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: setup-2.13.7-3.amzn2023.0.2.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: setup-2.13.7-3.amzn2023.0.2.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: filesystem-3.14-5.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: filesystem-3.14-5.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: basesystem-11-11.amzn2023.0.2.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: basesystem-11-11.amzn2023.0.2.noarch
2025-01-07T22:24:29Z SUBDEBUG Installed: glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: glibc-common-2.34-117.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: glibc-common-2.34-117.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: glibc-2.34-117.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: glibc-2.34-117.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: zlib-1.2.11-33.amzn2023.0.5.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: zlib-1.2.11-33.amzn2023.0.5.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libgpg-error-1.42-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libgpg-error-1.42-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: xz-libs-5.2.5-9.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: xz-libs-5.2.5-9.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libxml2-2.10.4-1.amzn2023.0.7.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libxml2-2.10.4-1.amzn2023.0.7.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libffi-3.4.4-1.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libffi-3.4.4-1.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libzstd-1.5.5-1.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libzstd-1.5.5-1.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: pcre2-10.40-1.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: pcre2-10.40-1.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: p11-kit-0.24.1-2.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: p11-kit-0.24.1-2.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libassuan-2.5.5-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libassuan-2.5.5-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: gmp-1:6.2.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: gmp-1:6.2.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libattr-2.5.1-3.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libattr-2.5.1-3.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libacl-2.3.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libacl-2.3.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libcap-2.48-2.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libcap-2.48-2.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libsmartcols-2.37.4-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libsmartcols-2.37.4-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libunistring-0.9.10-10.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libunistring-0.9.10-10.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libidn2-2.3.2-1.amzn2023.0.5.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libidn2-2.3.2-1.amzn2023.0.5.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libuuid-2.37.4-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libuuid-2.37.4-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: popt-1.18-6.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: popt-1.18-6.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: readline-8.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: readline-8.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libpsl-0.21.1-3.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libpsl-0.21.1-3.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: mpfr-4.1.0-7.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: mpfr-4.1.0-7.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: grep-3.8-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: grep-3.8-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libgcrypt-1.10.2-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libgcrypt-1.10.2-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: file-libs-5.39-7.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: file-libs-5.39-7.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: alternatives-1.15-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: alternatives-1.15-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: json-c-0.14-8.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: json-c-0.14-8.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libcap-ng-0.8.2-4.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libcap-ng-0.8.2-4.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: audit-libs-3.0.6-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: audit-libs-3.0.6-1.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libcom_err-1.46.5-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libcom_err-1.46.5-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libnghttp2-1.59.0-3.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libnghttp2-1.59.0-3.amzn2023.0.1.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libsepol-3.4-3.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libsepol-3.4-3.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libselinux-3.4-5.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libselinux-3.4-5.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: coreutils-single-8.32-30.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: coreutils-single-8.32-30.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: sed-4.8-7.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: sed-4.8-7.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libblkid-2.37.4-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libblkid-2.37.4-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libmount-2.37.4-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libmount-2.37.4-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libsigsegv-2.13-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libsigsegv-2.13-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: gawk-5.1.0-3.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: gawk-5.1.0-3.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libstdc++-11.4.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libstdc++-11.4.1-2.amzn2023.0.2.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libtasn1-4.19.0-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: libtasn1-4.19.0-1.amzn2023.0.4.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64
2025-01-07T22:24:29Z SUBDEBUG Installed: ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch
2025-01-07T22:24:30Z SUBDEBUG Installed: ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch
2025-01-07T22:24:30Z SUBDEBUG Installed: openssl-libs-1:3.0.8-1.amzn2023.0.18.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: openssl-libs-1:3.0.8-1.amzn2023.0.18.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: glib2-2.82.2-764.amzn2023.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: glib2-2.82.2-764.amzn2023.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: gobject-introspection-1.82.0-1.amzn2023.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: gobject-introspection-1.82.0-1.amzn2023.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libpeas-1.32.0-1.amzn2023.0.3.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libpeas-1.32.0-1.amzn2023.0.3.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libverto-0.3.2-1.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libverto-0.3.2-1.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: krb5-libs-1.21.3-1.amzn2023.0.1.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: krb5-libs-1.21.3-1.amzn2023.0.1.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: curl-minimal-8.5.0-1.amzn2023.0.4.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: curl-minimal-8.5.0-1.amzn2023.0.4.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libyaml-0.2.5-5.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libyaml-0.2.5-5.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: lua-libs-5.4.4-3.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: lua-libs-5.4.4-3.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: lz4-libs-1.9.4-1.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: lz4-libs-1.9.4-1.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libarchive-3.7.4-2.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libarchive-3.7.4-2.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: rpm-4.16.1.3-29.amzn2023.0.6.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: rpm-4.16.1.3-29.amzn2023.0.6.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libmodulemd-2.13.0-2.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libmodulemd-2.13.0-2.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libsolv-0.7.22-1.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libsolv-0.7.22-1.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: npth-1.6-6.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: npth-1.6-6.amzn2023.0.2.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: gpgme-1.15.1-6.amzn2023.0.3.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: gpgme-1.15.1-6.amzn2023.0.3.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: librepo-1.14.5-2.amzn2023.0.1.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: librepo-1.14.5-2.amzn2023.0.1.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libdnf-0.69.0-8.amzn2023.0.5.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libdnf-0.69.0-8.amzn2023.0.5.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch
2025-01-07T22:24:30Z SUBDEBUG Installed: libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch
2025-01-07T22:24:30Z SUBDEBUG Installed: dnf-data-4.14.0-1.amzn2023.0.5.noarch
2025-01-07T22:24:30Z SUBDEBUG Installed: dnf-data-4.14.0-1.amzn2023.0.5.noarch
2025-01-07T22:24:30Z SUBDEBUG Installed: microdnf-3.10.0-2.amzn2023.0.1.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: microdnf-3.10.0-2.amzn2023.0.1.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64
2025-01-07T22:24:30Z SUBDEBUG Installed: microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64
2025-01-07T22:24:32Z INFO --- logging initialized ---
2025-01-13T14:44:38+0000 INFO --- logging initialized ---
2025-01-13T14:44:47+0000 SUBDEBUG Installed: tzdata-2024a-1.amzn2023.0.1.noarch
2025-01-13T14:44:47+0000 INFO --- logging initialized ---
2025-01-13T14:44:48+0000 SUBDEBUG Installed: openssl-snapsafe-libs-1:3.0.8-1.amzn2023.0.18.aarch64
2025-01-13T14:44:48+0000 SUBDEBUG Erase: openssl-libs-1:3.0.8-1.amzn2023.0.18.aarch64
2025-01-13T14:45:01+0000 INFO --- logging initialized ---
2025-01-13T14:45:22+0000 INFO --- logging initialized ---
