2025-01-07T22:24:14Z INFO --- logging initialized ---
2025-01-07T22:24:14Z DDEBUG timer: config: 7 ms
2025-01-07T22:24:14Z DEBUG DNF version: 4.0.9
2025-01-07T22:24:14Z DDEBUG Command: dnf -c /tmp/imagebuilder-WlwwMy/dnfconf-eY6N8w -d10 -e10 --installroot=/tmp/imagebuilder-WlwwMy/imageroot -y shell /tmp/imagebuilder-WlwwMy/yshell-BOzkQd 
2025-01-07T22:24:14Z DDEBUG Installroot: /tmp/imagebuilder-WlwwMy/imageroot
2025-01-07T22:24:14Z DDEBUG Releasever: None
2025-01-07T22:24:14Z DEBUG cachedir: /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever
2025-01-07T22:24:14Z DDEBUG Base command: shell
2025-01-07T22:24:14Z DDEBUG Extra commands: [u'-c', u'/tmp/imagebuilder-WlwwMy/dnfconf-eY6N8w', u'-d10', u'-e10', u'--installroot=/tmp/imagebuilder-WlwwMy/imageroot', u'-y', u'shell', u'/tmp/imagebuilder-WlwwMy/yshell-BOzkQd']
2025-01-07T22:24:14Z DEBUG repo: downloading from remote: amazonlinux
2025-01-07T22:24:15Z DEBUG not found other for: Amazon Linux 2023 repository
2025-01-07T22:24:15Z DEBUG not found modules for: Amazon Linux 2023 repository
2025-01-07T22:24:15Z DEBUG not found deltainfo for: Amazon Linux 2023 repository
2025-01-07T22:24:24Z DEBUG amazonlinux: using metadata from Mon Jan  6 00:00:00 2025.
2025-01-07T22:24:24Z INFO Last metadata expiration check: 0:00:10 ago on Tue Jan  7 22:24:14 2025.
2025-01-07T22:24:24Z DEBUG No module defaults found
2025-01-07T22:24:25Z DDEBUG timer: sack setup: 10688 ms
2025-01-07T22:24:25Z DEBUG --> Starting dependency resolution
2025-01-07T22:24:25Z DEBUG ---> Package alternatives.aarch64 1.15-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package amazon-linux-repo-cdn.noarch 2023.6.20250107-0.amzn2023 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package audit-libs.aarch64 3.0.6-1.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package basesystem.noarch 11-11.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package bash.aarch64 5.2.15-1.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package bzip2-libs.aarch64 1.0.8-6.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package ca-certificates.noarch 2023.2.68-1.0.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package coreutils-single.aarch64 8.32-30.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package crypto-policies.noarch 20220428-1.gitdfb10ea.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package curl-minimal.aarch64 8.5.0-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package dnf-data.noarch 4.14.0-1.amzn2023.0.5 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package file-libs.aarch64 5.39-7.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package filesystem.aarch64 3.14-5.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package gawk.aarch64 5.1.0-3.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package glib2.aarch64 2.82.2-764.amzn2023 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package glibc.aarch64 2.34-117.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package glibc-common.aarch64 2.34-117.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package glibc-minimal-langpack.aarch64 2.34-117.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package gmp.aarch64 1:6.2.1-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package gnupg2-minimal.aarch64 2.3.7-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package gobject-introspection.aarch64 1.82.0-1.amzn2023 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package gpgme.aarch64 1.15.1-6.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package grep.aarch64 3.8-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package json-c.aarch64 0.14-8.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package keyutils-libs.aarch64 1.6.3-1.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package krb5-libs.aarch64 1.21.3-1.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libacl.aarch64 2.3.1-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libarchive.aarch64 3.7.4-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libassuan.aarch64 2.5.5-1.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libattr.aarch64 2.5.1-3.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libblkid.aarch64 2.37.4-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libcap.aarch64 2.48-2.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libcap-ng.aarch64 0.8.2-4.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libcom_err.aarch64 1.46.5-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libcurl-minimal.aarch64 8.5.0-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libdnf.aarch64 0.69.0-8.amzn2023.0.5 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libffi.aarch64 3.4.4-1.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libgcc.aarch64 11.4.1-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libgcrypt.aarch64 1.10.2-1.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libgpg-error.aarch64 1.42-1.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libidn2.aarch64 2.3.2-1.amzn2023.0.5 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libmodulemd.aarch64 2.13.0-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libmount.aarch64 2.37.4-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libnghttp2.aarch64 1.59.0-3.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libpeas.aarch64 1.32.0-1.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libpsl.aarch64 0.21.1-3.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package librepo.aarch64 1.14.5-2.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libreport-filesystem.noarch 2.15.2-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libselinux.aarch64 3.4-5.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libsepol.aarch64 3.4-3.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libsigsegv.aarch64 2.13-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libsmartcols.aarch64 2.37.4-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libsolv.aarch64 0.7.22-1.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libstdc++.aarch64 11.4.1-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libtasn1.aarch64 4.19.0-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libunistring.aarch64 0.9.10-10.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libuuid.aarch64 2.37.4-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libverto.aarch64 0.3.2-1.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libxml2.aarch64 2.10.4-1.amzn2023.0.7 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libyaml.aarch64 0.2.5-5.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package libzstd.aarch64 1.5.5-1.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package lua-libs.aarch64 5.4.4-3.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package lz4-libs.aarch64 1.9.4-1.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package microdnf.aarch64 3.10.0-2.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package microdnf-dnf.aarch64 3.10.0-2.amzn2023.0.1 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package mpfr.aarch64 4.1.0-7.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package ncurses-base.noarch 6.2-4.20200222.amzn2023.0.6 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package ncurses-libs.aarch64 6.2-4.20200222.amzn2023.0.6 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package npth.aarch64 1.6-6.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package openssl-libs.aarch64 1:3.0.8-1.amzn2023.0.18 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package p11-kit.aarch64 0.24.1-2.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package p11-kit-trust.aarch64 0.24.1-2.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package pcre2.aarch64 10.40-1.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package pcre2-syntax.noarch 10.40-1.amzn2023.0.3 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package popt.aarch64 1.18-6.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package publicsuffix-list-dafsa.noarch 20240212-61.amzn2023 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package readline.aarch64 8.1-2.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package rpm.aarch64 4.16.1.3-29.amzn2023.0.6 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package rpm-libs.aarch64 4.16.1.3-29.amzn2023.0.6 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package sed.aarch64 4.8-7.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package setup.noarch 2.13.7-3.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package sqlite-libs.aarch64 3.40.0-1.amzn2023.0.4 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package system-release.noarch 2023.6.20250107-0.amzn2023 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package xz-libs.aarch64 5.2.5-9.amzn2023.0.2 will be installed
2025-01-07T22:24:25Z DEBUG ---> Package zlib.aarch64 1.2.11-33.amzn2023.0.5 will be installed
2025-01-07T22:24:25Z DEBUG --> Finished dependency resolution
2025-01-07T22:24:25Z DDEBUG timer: depsolve: 29 ms
2025-01-07T22:24:25Z INFO ================================================================================
 Package                 Arch    Version                      Repository   Size
================================================================================
Installing:
 amazon-linux-repo-cdn   noarch  2023.6.20250107-0.amzn2023   amazonlinux 8.8 k
 coreutils-single        aarch64 8.32-30.amzn2023.0.3         amazonlinux 609 k
 curl-minimal            aarch64 8.5.0-1.amzn2023.0.4         amazonlinux 156 k
 glibc-minimal-langpack  aarch64 2.34-117.amzn2023.0.1        amazonlinux  21 k
 gnupg2-minimal          aarch64 2.3.7-1.amzn2023.0.4         amazonlinux 411 k
 libcurl-minimal         aarch64 8.5.0-1.amzn2023.0.4         amazonlinux 271 k
 microdnf                aarch64 3.10.0-2.amzn2023.0.1        amazonlinux  51 k
 microdnf-dnf            aarch64 3.10.0-2.amzn2023.0.1        amazonlinux 9.4 k
 system-release          noarch  2023.6.20250107-0.amzn2023   amazonlinux  20 k
Installing dependencies:
 alternatives            aarch64 1.15-2.amzn2023.0.2          amazonlinux  36 k
 audit-libs              aarch64 3.0.6-1.amzn2023.0.2         amazonlinux 116 k
 basesystem              noarch  11-11.amzn2023.0.2           amazonlinux 7.8 k
 bash                    aarch64 5.2.15-1.amzn2023.0.2        amazonlinux 1.8 M
 bzip2-libs              aarch64 1.0.8-6.amzn2023.0.2         amazonlinux  44 k
 ca-certificates         noarch  2023.2.68-1.0.amzn2023.0.1   amazonlinux 735 k
 crypto-policies         noarch  20220428-1.gitdfb10ea.amzn2023.0.2
                                                              amazonlinux  60 k
 dnf-data                noarch  4.14.0-1.amzn2023.0.5        amazonlinux  34 k
 file-libs               aarch64 5.39-7.amzn2023.0.4          amazonlinux 587 k
 filesystem              aarch64 3.14-5.amzn2023.0.3          amazonlinux  22 k
 gawk                    aarch64 5.1.0-3.amzn2023.0.3         amazonlinux 984 k
 glib2                   aarch64 2.82.2-764.amzn2023          amazonlinux 3.0 M
 glibc                   aarch64 2.34-117.amzn2023.0.1        amazonlinux 1.7 M
 glibc-common            aarch64 2.34-117.amzn2023.0.1        amazonlinux 284 k
 gmp                     aarch64 1:6.2.1-2.amzn2023.0.2       amazonlinux 276 k
 gobject-introspection   aarch64 1.82.0-1.amzn2023            amazonlinux 120 k
 gpgme                   aarch64 1.15.1-6.amzn2023.0.3        amazonlinux 204 k
 grep                    aarch64 3.8-1.amzn2023.0.4           amazonlinux 282 k
 json-c                  aarch64 0.14-8.amzn2023.0.2          amazonlinux  40 k
 keyutils-libs           aarch64 1.6.3-1.amzn2023.0.1         amazonlinux  32 k
 krb5-libs               aarch64 1.21.3-1.amzn2023.0.1        amazonlinux 768 k
 libacl                  aarch64 2.3.1-2.amzn2023.0.2         amazonlinux  24 k
 libarchive              aarch64 3.7.4-2.amzn2023.0.2         amazonlinux 411 k
 libassuan               aarch64 2.5.5-1.amzn2023.0.2         amazonlinux  67 k
 libattr                 aarch64 2.5.1-3.amzn2023.0.2         amazonlinux  19 k
 libblkid                aarch64 2.37.4-1.amzn2023.0.4        amazonlinux 105 k
 libcap                  aarch64 2.48-2.amzn2023.0.3          amazonlinux  68 k
 libcap-ng               aarch64 0.8.2-4.amzn2023.0.2         amazonlinux  33 k
 libcom_err              aarch64 1.46.5-2.amzn2023.0.2        amazonlinux  27 k
 libdnf                  aarch64 0.69.0-8.amzn2023.0.5        amazonlinux 606 k
 libffi                  aarch64 3.4.4-1.amzn2023.0.1         amazonlinux  37 k
 libgcc                  aarch64 11.4.1-2.amzn2023.0.2        amazonlinux  87 k
 libgcrypt               aarch64 1.10.2-1.amzn2023.0.2        amazonlinux 481 k
 libgpg-error            aarch64 1.42-1.amzn2023.0.2          amazonlinux 213 k
 libidn2                 aarch64 2.3.2-1.amzn2023.0.5         amazonlinux 105 k
 libmodulemd             aarch64 2.13.0-2.amzn2023.0.2        amazonlinux 207 k
 libmount                aarch64 2.37.4-1.amzn2023.0.4        amazonlinux 130 k
 libnghttp2              aarch64 1.59.0-3.amzn2023.0.1        amazonlinux  78 k
 libpeas                 aarch64 1.32.0-1.amzn2023.0.3        amazonlinux 119 k
 libpsl                  aarch64 0.21.1-3.amzn2023.0.2        amazonlinux  61 k
 librepo                 aarch64 1.14.5-2.amzn2023.0.1        amazonlinux  87 k
 libreport-filesystem    noarch  2.15.2-2.amzn2023.0.2        amazonlinux  10 k
 libselinux              aarch64 3.4-5.amzn2023.0.2           amazonlinux  87 k
 libsepol                aarch64 3.4-3.amzn2023.0.3           amazonlinux 311 k
 libsigsegv              aarch64 2.13-2.amzn2023.0.2          amazonlinux  28 k
 libsmartcols            aarch64 2.37.4-1.amzn2023.0.4        amazonlinux  60 k
 libsolv                 aarch64 0.7.22-1.amzn2023.0.2        amazonlinux 379 k
 libstdc++               aarch64 11.4.1-2.amzn2023.0.2        amazonlinux 691 k
 libtasn1                aarch64 4.19.0-1.amzn2023.0.4        amazonlinux  75 k
 libunistring            aarch64 0.9.10-10.amzn2023.0.2       amazonlinux 493 k
 libuuid                 aarch64 2.37.4-1.amzn2023.0.4        amazonlinux  26 k
 libverto                aarch64 0.3.2-1.amzn2023.0.2         amazonlinux  22 k
 libxml2                 aarch64 2.10.4-1.amzn2023.0.7        amazonlinux 690 k
 libyaml                 aarch64 0.2.5-5.amzn2023.0.2         amazonlinux  60 k
 libzstd                 aarch64 1.5.5-1.amzn2023.0.1         amazonlinux 284 k
 lua-libs                aarch64 5.4.4-3.amzn2023.0.2         amazonlinux 214 k
 lz4-libs                aarch64 1.9.4-1.amzn2023.0.2         amazonlinux  73 k
 mpfr                    aarch64 4.1.0-7.amzn2023.0.2         amazonlinux 243 k
 ncurses-base            noarch  6.2-4.20200222.amzn2023.0.6  amazonlinux  60 k
 ncurses-libs            aarch64 6.2-4.20200222.amzn2023.0.6  amazonlinux 318 k
 npth                    aarch64 1.6-6.amzn2023.0.2           amazonlinux  25 k
 openssl-libs            aarch64 1:3.0.8-1.amzn2023.0.18      amazonlinux 2.1 M
 p11-kit                 aarch64 0.24.1-2.amzn2023.0.3        amazonlinux 346 k
 p11-kit-trust           aarch64 0.24.1-2.amzn2023.0.3        amazonlinux 138 k
 pcre2                   aarch64 10.40-1.amzn2023.0.3         amazonlinux 220 k
 pcre2-syntax            noarch  10.40-1.amzn2023.0.3         amazonlinux 142 k
 popt                    aarch64 1.18-6.amzn2023.0.2          amazonlinux  59 k
 publicsuffix-list-dafsa noarch  20240212-61.amzn2023         amazonlinux  59 k
 readline                aarch64 8.1-2.amzn2023.0.2           amazonlinux 212 k
 rpm                     aarch64 4.16.1.3-29.amzn2023.0.6     amazonlinux 485 k
 rpm-libs                aarch64 4.16.1.3-29.amzn2023.0.6     amazonlinux 308 k
 sed                     aarch64 4.8-7.amzn2023.0.2           amazonlinux 295 k
 setup                   noarch  2.13.7-3.amzn2023.0.2        amazonlinux 141 k
 sqlite-libs             aarch64 3.40.0-1.amzn2023.0.4        amazonlinux 660 k
 xz-libs                 aarch64 5.2.5-9.amzn2023.0.2         amazonlinux  92 k
 zlib                    aarch64 1.2.11-33.amzn2023.0.5       amazonlinux  91 k

Transaction Summary
================================================================================
Install  85 Packages

2025-01-07T22:24:25Z INFO ================================================================================
 Package                 Arch    Version                      Repository   Size
================================================================================
Installing:
 amazon-linux-repo-cdn   noarch  2023.6.20250107-0.amzn2023   amazonlinux 8.8 k
 coreutils-single        aarch64 8.32-30.amzn2023.0.3         amazonlinux 609 k
 curl-minimal            aarch64 8.5.0-1.amzn2023.0.4         amazonlinux 156 k
 glibc-minimal-langpack  aarch64 2.34-117.amzn2023.0.1        amazonlinux  21 k
 gnupg2-minimal          aarch64 2.3.7-1.amzn2023.0.4         amazonlinux 411 k
 libcurl-minimal         aarch64 8.5.0-1.amzn2023.0.4         amazonlinux 271 k
 microdnf                aarch64 3.10.0-2.amzn2023.0.1        amazonlinux  51 k
 microdnf-dnf            aarch64 3.10.0-2.amzn2023.0.1        amazonlinux 9.4 k
 system-release          noarch  2023.6.20250107-0.amzn2023   amazonlinux  20 k
Installing dependencies:
 alternatives            aarch64 1.15-2.amzn2023.0.2          amazonlinux  36 k
 audit-libs              aarch64 3.0.6-1.amzn2023.0.2         amazonlinux 116 k
 basesystem              noarch  11-11.amzn2023.0.2           amazonlinux 7.8 k
 bash                    aarch64 5.2.15-1.amzn2023.0.2        amazonlinux 1.8 M
 bzip2-libs              aarch64 1.0.8-6.amzn2023.0.2         amazonlinux  44 k
 ca-certificates         noarch  2023.2.68-1.0.amzn2023.0.1   amazonlinux 735 k
 crypto-policies         noarch  20220428-1.gitdfb10ea.amzn2023.0.2
                                                              amazonlinux  60 k
 dnf-data                noarch  4.14.0-1.amzn2023.0.5        amazonlinux  34 k
 file-libs               aarch64 5.39-7.amzn2023.0.4          amazonlinux 587 k
 filesystem              aarch64 3.14-5.amzn2023.0.3          amazonlinux  22 k
 gawk                    aarch64 5.1.0-3.amzn2023.0.3         amazonlinux 984 k
 glib2                   aarch64 2.82.2-764.amzn2023          amazonlinux 3.0 M
 glibc                   aarch64 2.34-117.amzn2023.0.1        amazonlinux 1.7 M
 glibc-common            aarch64 2.34-117.amzn2023.0.1        amazonlinux 284 k
 gmp                     aarch64 1:6.2.1-2.amzn2023.0.2       amazonlinux 276 k
 gobject-introspection   aarch64 1.82.0-1.amzn2023            amazonlinux 120 k
 gpgme                   aarch64 1.15.1-6.amzn2023.0.3        amazonlinux 204 k
 grep                    aarch64 3.8-1.amzn2023.0.4           amazonlinux 282 k
 json-c                  aarch64 0.14-8.amzn2023.0.2          amazonlinux  40 k
 keyutils-libs           aarch64 1.6.3-1.amzn2023.0.1         amazonlinux  32 k
 krb5-libs               aarch64 1.21.3-1.amzn2023.0.1        amazonlinux 768 k
 libacl                  aarch64 2.3.1-2.amzn2023.0.2         amazonlinux  24 k
 libarchive              aarch64 3.7.4-2.amzn2023.0.2         amazonlinux 411 k
 libassuan               aarch64 2.5.5-1.amzn2023.0.2         amazonlinux  67 k
 libattr                 aarch64 2.5.1-3.amzn2023.0.2         amazonlinux  19 k
 libblkid                aarch64 2.37.4-1.amzn2023.0.4        amazonlinux 105 k
 libcap                  aarch64 2.48-2.amzn2023.0.3          amazonlinux  68 k
 libcap-ng               aarch64 0.8.2-4.amzn2023.0.2         amazonlinux  33 k
 libcom_err              aarch64 1.46.5-2.amzn2023.0.2        amazonlinux  27 k
 libdnf                  aarch64 0.69.0-8.amzn2023.0.5        amazonlinux 606 k
 libffi                  aarch64 3.4.4-1.amzn2023.0.1         amazonlinux  37 k
 libgcc                  aarch64 11.4.1-2.amzn2023.0.2        amazonlinux  87 k
 libgcrypt               aarch64 1.10.2-1.amzn2023.0.2        amazonlinux 481 k
 libgpg-error            aarch64 1.42-1.amzn2023.0.2          amazonlinux 213 k
 libidn2                 aarch64 2.3.2-1.amzn2023.0.5         amazonlinux 105 k
 libmodulemd             aarch64 2.13.0-2.amzn2023.0.2        amazonlinux 207 k
 libmount                aarch64 2.37.4-1.amzn2023.0.4        amazonlinux 130 k
 libnghttp2              aarch64 1.59.0-3.amzn2023.0.1        amazonlinux  78 k
 libpeas                 aarch64 1.32.0-1.amzn2023.0.3        amazonlinux 119 k
 libpsl                  aarch64 0.21.1-3.amzn2023.0.2        amazonlinux  61 k
 librepo                 aarch64 1.14.5-2.amzn2023.0.1        amazonlinux  87 k
 libreport-filesystem    noarch  2.15.2-2.amzn2023.0.2        amazonlinux  10 k
 libselinux              aarch64 3.4-5.amzn2023.0.2           amazonlinux  87 k
 libsepol                aarch64 3.4-3.amzn2023.0.3           amazonlinux 311 k
 libsigsegv              aarch64 2.13-2.amzn2023.0.2          amazonlinux  28 k
 libsmartcols            aarch64 2.37.4-1.amzn2023.0.4        amazonlinux  60 k
 libsolv                 aarch64 0.7.22-1.amzn2023.0.2        amazonlinux 379 k
 libstdc++               aarch64 11.4.1-2.amzn2023.0.2        amazonlinux 691 k
 libtasn1                aarch64 4.19.0-1.amzn2023.0.4        amazonlinux  75 k
 libunistring            aarch64 0.9.10-10.amzn2023.0.2       amazonlinux 493 k
 libuuid                 aarch64 2.37.4-1.amzn2023.0.4        amazonlinux  26 k
 libverto                aarch64 0.3.2-1.amzn2023.0.2         amazonlinux  22 k
 libxml2                 aarch64 2.10.4-1.amzn2023.0.7        amazonlinux 690 k
 libyaml                 aarch64 0.2.5-5.amzn2023.0.2         amazonlinux  60 k
 libzstd                 aarch64 1.5.5-1.amzn2023.0.1         amazonlinux 284 k
 lua-libs                aarch64 5.4.4-3.amzn2023.0.2         amazonlinux 214 k
 lz4-libs                aarch64 1.9.4-1.amzn2023.0.2         amazonlinux  73 k
 mpfr                    aarch64 4.1.0-7.amzn2023.0.2         amazonlinux 243 k
 ncurses-base            noarch  6.2-4.20200222.amzn2023.0.6  amazonlinux  60 k
 ncurses-libs            aarch64 6.2-4.20200222.amzn2023.0.6  amazonlinux 318 k
 npth                    aarch64 1.6-6.amzn2023.0.2           amazonlinux  25 k
 openssl-libs            aarch64 1:3.0.8-1.amzn2023.0.18      amazonlinux 2.1 M
 p11-kit                 aarch64 0.24.1-2.amzn2023.0.3        amazonlinux 346 k
 p11-kit-trust           aarch64 0.24.1-2.amzn2023.0.3        amazonlinux 138 k
 pcre2                   aarch64 10.40-1.amzn2023.0.3         amazonlinux 220 k
 pcre2-syntax            noarch  10.40-1.amzn2023.0.3         amazonlinux 142 k
 popt                    aarch64 1.18-6.amzn2023.0.2          amazonlinux  59 k
 publicsuffix-list-dafsa noarch  20240212-61.amzn2023         amazonlinux  59 k
 readline                aarch64 8.1-2.amzn2023.0.2           amazonlinux 212 k
 rpm                     aarch64 4.16.1.3-29.amzn2023.0.6     amazonlinux 485 k
 rpm-libs                aarch64 4.16.1.3-29.amzn2023.0.6     amazonlinux 308 k
 sed                     aarch64 4.8-7.amzn2023.0.2           amazonlinux 295 k
 setup                   noarch  2.13.7-3.amzn2023.0.2        amazonlinux 141 k
 sqlite-libs             aarch64 3.40.0-1.amzn2023.0.4        amazonlinux 660 k
 xz-libs                 aarch64 5.2.5-9.amzn2023.0.2         amazonlinux  92 k
 zlib                    aarch64 1.2.11-33.amzn2023.0.5       amazonlinux  91 k

Transaction Summary
================================================================================
Install  85 Packages

2025-01-07T22:24:25Z INFO Total download size: 25 M
2025-01-07T22:24:25Z INFO Installed size: 109 M
2025-01-07T22:24:25Z INFO Downloading Packages:
2025-01-07T22:24:27Z INFO --------------------------------------------------------------------------------
2025-01-07T22:24:27Z INFO Total                                            11 MB/s |  25 MB     00:02     
2025-01-07T22:24:27Z INFO Running transaction check
2025-01-07T22:24:27Z INFO Transaction check succeeded.
2025-01-07T22:24:27Z INFO Running transaction test
2025-01-07T22:24:28Z INFO Transaction test succeeded.
2025-01-07T22:24:28Z DDEBUG timer: transaction test: 178 ms
2025-01-07T22:24:28Z INFO Running transaction
2025-01-07T22:24:28Z DEBUG RPMDB altered outside of DNF.
2025-01-07T22:24:29Z DDEBUG RPM transaction start.
2025-01-07T22:24:31Z DDEBUG RPM transaction over.
2025-01-07T22:24:32Z DDEBUG timer: verify transaction: 406 ms
2025-01-07T22:24:32Z DDEBUG timer: transaction: 4254 ms
2025-01-07T22:24:32Z INFO 
Installed:
  amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch                       
  coreutils-single-8.32-30.amzn2023.0.3.aarch64                                 
  curl-minimal-8.5.0-1.amzn2023.0.4.aarch64                                     
  glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64                          
  gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64                                   
  libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64                                  
  microdnf-3.10.0-2.amzn2023.0.1.aarch64                                        
  microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64                                    
  system-release-2023.6.20250107-0.amzn2023.noarch                              
  alternatives-1.15-2.amzn2023.0.2.aarch64                                      
  audit-libs-3.0.6-1.amzn2023.0.2.aarch64                                       
  basesystem-11-11.amzn2023.0.2.noarch                                          
  bash-5.2.15-1.amzn2023.0.2.aarch64                                            
  bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64                                       
  ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch                             
  crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch                     
  dnf-data-4.14.0-1.amzn2023.0.5.noarch                                         
  file-libs-5.39-7.amzn2023.0.4.aarch64                                         
  filesystem-3.14-5.amzn2023.0.3.aarch64                                        
  gawk-5.1.0-3.amzn2023.0.3.aarch64                                             
  glib2-2.82.2-764.amzn2023.aarch64                                             
  glibc-2.34-117.amzn2023.0.1.aarch64                                           
  glibc-common-2.34-117.amzn2023.0.1.aarch64                                    
  gmp-1:6.2.1-2.amzn2023.0.2.aarch64                                            
  gobject-introspection-1.82.0-1.amzn2023.aarch64                               
  gpgme-1.15.1-6.amzn2023.0.3.aarch64                                           
  grep-3.8-1.amzn2023.0.4.aarch64                                               
  json-c-0.14-8.amzn2023.0.2.aarch64                                            
  keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64                                    
  krb5-libs-1.21.3-1.amzn2023.0.1.aarch64                                       
  libacl-2.3.1-2.amzn2023.0.2.aarch64                                           
  libarchive-3.7.4-2.amzn2023.0.2.aarch64                                       
  libassuan-2.5.5-1.amzn2023.0.2.aarch64                                        
  libattr-2.5.1-3.amzn2023.0.2.aarch64                                          
  libblkid-2.37.4-1.amzn2023.0.4.aarch64                                        
  libcap-2.48-2.amzn2023.0.3.aarch64                                            
  libcap-ng-0.8.2-4.amzn2023.0.2.aarch64                                        
  libcom_err-1.46.5-2.amzn2023.0.2.aarch64                                      
  libdnf-0.69.0-8.amzn2023.0.5.aarch64                                          
  libffi-3.4.4-1.amzn2023.0.1.aarch64                                           
  libgcc-11.4.1-2.amzn2023.0.2.aarch64                                          
  libgcrypt-1.10.2-1.amzn2023.0.2.aarch64                                       
  libgpg-error-1.42-1.amzn2023.0.2.aarch64                                      
  libidn2-2.3.2-1.amzn2023.0.5.aarch64                                          
  libmodulemd-2.13.0-2.amzn2023.0.2.aarch64                                     
  libmount-2.37.4-1.amzn2023.0.4.aarch64                                        
  libnghttp2-1.59.0-3.amzn2023.0.1.aarch64                                      
  libpeas-1.32.0-1.amzn2023.0.3.aarch64                                         
  libpsl-0.21.1-3.amzn2023.0.2.aarch64                                          
  librepo-1.14.5-2.amzn2023.0.1.aarch64                                         
  libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch                             
  libselinux-3.4-5.amzn2023.0.2.aarch64                                         
  libsepol-3.4-3.amzn2023.0.3.aarch64                                           
  libsigsegv-2.13-2.amzn2023.0.2.aarch64                                        
  libsmartcols-2.37.4-1.amzn2023.0.4.aarch64                                    
  libsolv-0.7.22-1.amzn2023.0.2.aarch64                                         
  libstdc++-11.4.1-2.amzn2023.0.2.aarch64                                       
  libtasn1-4.19.0-1.amzn2023.0.4.aarch64                                        
  libunistring-0.9.10-10.amzn2023.0.2.aarch64                                   
  libuuid-2.37.4-1.amzn2023.0.4.aarch64                                         
  libverto-0.3.2-1.amzn2023.0.2.aarch64                                         
  libxml2-2.10.4-1.amzn2023.0.7.aarch64                                         
  libyaml-0.2.5-5.amzn2023.0.2.aarch64                                          
  libzstd-1.5.5-1.amzn2023.0.1.aarch64                                          
  lua-libs-5.4.4-3.amzn2023.0.2.aarch64                                         
  lz4-libs-1.9.4-1.amzn2023.0.2.aarch64                                         
  mpfr-4.1.0-7.amzn2023.0.2.aarch64                                             
  ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch                               
  ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64                              
  npth-1.6-6.amzn2023.0.2.aarch64                                               
  openssl-libs-1:3.0.8-1.amzn2023.0.18.aarch64                                  
  p11-kit-0.24.1-2.amzn2023.0.3.aarch64                                         
  p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64                                   
  pcre2-10.40-1.amzn2023.0.3.aarch64                                            
  pcre2-syntax-10.40-1.amzn2023.0.3.noarch                                      
  popt-1.18-6.amzn2023.0.2.aarch64                                              
  publicsuffix-list-dafsa-20240212-61.amzn2023.noarch                           
  readline-8.1-2.amzn2023.0.2.aarch64                                           
  rpm-4.16.1.3-29.amzn2023.0.6.aarch64                                          
  rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64                                     
  sed-4.8-7.amzn2023.0.2.aarch64                                                
  setup-2.13.7-3.amzn2023.0.2.noarch                                            
  sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64                                     
  xz-libs-5.2.5-9.amzn2023.0.2.aarch64                                          
  zlib-1.2.11-33.amzn2023.0.5.aarch64                                           

2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libidn2-2.3.2-1.amzn2023.0.5.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libunistring-0.9.10-10.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libmount-2.37.4-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/grep-3.8-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libacl-2.3.1-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/publicsuffix-list-dafsa-20240212-61.amzn2023.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/alternatives-1.15-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/npth-1.6-6.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libsigsegv-2.13-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libuuid-2.37.4-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/readline-8.1-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/pcre2-10.40-1.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libffi-3.4.4-1.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libmodulemd-2.13.0-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libverto-0.3.2-1.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libgcc-11.4.1-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libcap-2.48-2.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/pcre2-syntax-10.40-1.amzn2023.0.3.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/rpm-4.16.1.3-29.amzn2023.0.6.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/librepo-1.14.5-2.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libsolv-0.7.22-1.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libpsl-0.21.1-3.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/system-release-2023.6.20250107-0.amzn2023.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libgcrypt-1.10.2-1.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/dnf-data-4.14.0-1.amzn2023.0.5.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libgpg-error-1.42-1.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libtasn1-4.19.0-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/coreutils-single-8.32-30.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libblkid-2.37.4-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/file-libs-5.39-7.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/mpfr-4.1.0-7.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/openssl-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gpgme-1.15.1-6.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/filesystem-3.14-5.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/audit-libs-3.0.6-1.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/xz-libs-5.2.5-9.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/glibc-2.34-117.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/lz4-libs-1.9.4-1.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libsepol-3.4-3.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/krb5-libs-1.21.3-1.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libpeas-1.32.0-1.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libcap-ng-0.8.2-4.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/glib2-2.82.2-764.amzn2023.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libassuan-2.5.5-1.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libxml2-2.10.4-1.amzn2023.0.7.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/curl-minimal-8.5.0-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libselinux-3.4-5.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/popt-1.18-6.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libstdc++-11.4.1-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/lua-libs-5.4.4-3.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libnghttp2-1.59.0-3.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libzstd-1.5.5-1.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libsmartcols-2.37.4-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/sed-4.8-7.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/json-c-0.14-8.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/microdnf-3.10.0-2.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/setup-2.13.7-3.amzn2023.0.2.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/p11-kit-0.24.1-2.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libcom_err-1.46.5-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libattr-2.5.1-3.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libdnf-0.69.0-8.amzn2023.0.5.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/bash-5.2.15-1.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gobject-introspection-1.82.0-1.amzn2023.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/glibc-common-2.34-117.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/basesystem-11-11.amzn2023.0.2.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gmp-6.2.1-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libarchive-3.7.4-2.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/gawk-5.1.0-3.amzn2023.0.3.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/zlib-1.2.11-33.amzn2023.0.5.aarch64.rpm removed
2025-01-07T22:24:32Z DDEBUG /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/$releasever/amazonlinux-77515b015c4db925/packages/libyaml-0.2.5-5.amzn2023.0.2.aarch64.rpm removed
2025-01-07T22:24:32Z DEBUG repo: using cache for: amazonlinux
2025-01-07T22:24:32Z DEBUG not found other for: Amazon Linux 2023 repository
2025-01-07T22:24:32Z DEBUG not found modules for: Amazon Linux 2023 repository
2025-01-07T22:24:32Z DEBUG not found deltainfo for: Amazon Linux 2023 repository
2025-01-07T22:24:32Z DEBUG amazonlinux: using metadata from Mon Jan  6 00:00:00 2025.
2025-01-07T22:24:32Z INFO Last metadata expiration check: 0:00:18 ago on Tue Jan  7 22:24:14 2025.
2025-01-07T22:24:32Z DEBUG No module defaults found
2025-01-07T22:24:32Z DDEBUG timer: sack setup: 123 ms
2025-01-07T22:24:32Z DEBUG --> Starting dependency resolution
2025-01-07T22:24:32Z DEBUG --> Finished dependency resolution
2025-01-07T22:24:32Z DDEBUG timer: depsolve: 25 ms
2025-01-07T22:24:32Z INFO Dependencies resolved.
2025-01-07T22:24:32Z INFO Nothing to do.
2025-01-07T22:24:32Z INFO Complete!
2025-01-07T22:24:32Z DDEBUG Cleaning up.
2025-01-07T22:24:32Z INFO The downloaded packages were saved in cache until the next successful transaction.
2025-01-07T22:24:32Z INFO You can remove cached packages by executing 'dnf clean packages'.
2025-01-07T22:24:32Z INFO --- logging initialized ---
2025-01-07T22:24:32Z DDEBUG timer: config: 2 ms
2025-01-07T22:24:32Z DEBUG DNF version: 4.0.9
2025-01-07T22:24:32Z DDEBUG Command: dnf -c /tmp/imagebuilder-WlwwMy/dnfconf-hkomRv -d10 -e10 --installroot=/tmp/imagebuilder-WlwwMy/imageroot -y clean all 
2025-01-07T22:24:32Z DDEBUG Installroot: /tmp/imagebuilder-WlwwMy/imageroot
2025-01-07T22:24:32Z DDEBUG Releasever: 2023.6.20250107
2025-01-07T22:24:32Z DEBUG cachedir: /tmp/imagebuilder-WlwwMy/imageroot/tmp/var/cache/dnf/aarch64/2023.6.20250107
2025-01-07T22:24:32Z DDEBUG Base command: clean
2025-01-07T22:24:32Z DDEBUG Extra commands: [u'-c', u'/tmp/imagebuilder-WlwwMy/dnfconf-hkomRv', u'-d10', u'-e10', u'--installroot=/tmp/imagebuilder-WlwwMy/imageroot', u'-y', u'clean', u'all']
2025-01-07T22:24:32Z DEBUG Cleaning data: packages dbcache metadata
2025-01-07T22:24:32Z INFO 0 files removed
2025-01-07T22:24:32Z DDEBUG Cleaning up.
2025-01-13T14:44:38+0000 INFO --- logging initialized ---
2025-01-13T14:44:38+0000 DDEBUG timer: config: 13 ms
2025-01-13T14:44:38+0000 DEBUG DNF version: 4.14.0
2025-01-13T14:44:38+0000 DDEBUG Command: dnf -d1 -y --installroot=/tmp/root --setopt=install_weak_deps=False install tzdata.noarch 
2025-01-13T14:44:38+0000 DDEBUG Installroot: /tmp/root
2025-01-13T14:44:38+0000 DDEBUG Releasever: 2023.6.20250107
2025-01-13T14:44:38+0000 DEBUG cachedir: /tmp/root/var/cache/dnf
2025-01-13T14:44:38+0000 DDEBUG Base command: install
2025-01-13T14:44:38+0000 DDEBUG Extra commands: ['-d1', '-y', '--installroot=/tmp/root', '--setopt=install_weak_deps=False', 'install', 'tzdata.noarch']
2025-01-13T14:44:38+0000 DEBUG User-Agent: constructed: 'libdnf (Amazon Linux 2023; generic; Linux.aarch64)'
2025-01-13T14:44:38+0000 DEBUG repo: downloading from remote: amazonlinux
2025-01-13T14:44:38+0000 DEBUG Failed to retrieve a default SELinux context
2025-01-13T14:44:38+0000 DEBUG Failed to open a SELinux labeling handle: No such file or directory
2025-01-13T14:44:46+0000 DEBUG amazonlinux: using metadata from Mon Jan  6 00:00:00 2025.
2025-01-13T14:44:46+0000 INFO Last metadata expiration check: 0:00:08 ago on Mon Jan 13 14:44:38 2025.
2025-01-13T14:44:46+0000 DDEBUG timer: sack setup: 8188 ms
2025-01-13T14:44:47+0000 DEBUG --> Starting dependency resolution
2025-01-13T14:44:47+0000 DEBUG ---> Package tzdata.noarch 2024a-1.amzn2023.0.1 will be installed
2025-01-13T14:44:47+0000 DEBUG --> Finished dependency resolution
2025-01-13T14:44:47+0000 DDEBUG timer: depsolve: 50 ms
2025-01-13T14:44:47+0000 INFO Dependencies resolved.
2025-01-13T14:44:47+0000 INFO ================================================================================
 Package      Architecture Version                      Repository         Size
================================================================================
Installing:
 tzdata       noarch       2024a-1.amzn2023.0.1         amazonlinux       430 k

Transaction Summary
================================================================================
Install  1 Package

2025-01-13T14:44:47+0000 INFO Total download size: 430 k
2025-01-13T14:44:47+0000 INFO Installed size: 1.6 M
2025-01-13T14:44:47+0000 INFO Downloading Packages:
2025-01-13T14:44:47+0000 INFO --------------------------------------------------------------------------------
2025-01-13T14:44:47+0000 INFO Total                                           2.2 MB/s | 430 kB     00:00     
2025-01-13T14:44:47+0000 DEBUG Using rpmkeys executable at /usr/bin/rpmkeys to verify signatures
2025-01-13T14:44:47+0000 INFO Running transaction check
2025-01-13T14:44:47+0000 INFO Transaction check succeeded.
2025-01-13T14:44:47+0000 INFO Running transaction test
2025-01-13T14:44:47+0000 INFO Transaction test succeeded.
2025-01-13T14:44:47+0000 DDEBUG timer: transaction test: 15 ms
2025-01-13T14:44:47+0000 INFO Running transaction
2025-01-13T14:44:47+0000 DEBUG RPMDB altered outside of DNF.
2025-01-13T14:44:47+0000 DDEBUG RPM transaction start.
2025-01-13T14:44:47+0000 DDEBUG RPM transaction over.
2025-01-13T14:44:47+0000 DDEBUG timer: verify transaction: 12 ms
2025-01-13T14:44:47+0000 DDEBUG timer: transaction: 288 ms
2025-01-13T14:44:47+0000 DEBUG Installed: tzdata-2024a-1.amzn2023.0.1.noarch
2025-01-13T14:44:47+0000 INFO Complete!
2025-01-13T14:44:47+0000 DDEBUG Cleaning up.
2025-01-13T14:44:47+0000 DDEBUG /tmp/root/var/cache/dnf/amazonlinux-794d0ccad44a7436/packages/tzdata-2024a-1.amzn2023.0.1.noarch.rpm removed
2025-01-13T14:44:47+0000 INFO --- logging initialized ---
2025-01-13T14:44:47+0000 DDEBUG timer: config: 1 ms
2025-01-13T14:44:47+0000 DEBUG DNF version: 4.14.0
2025-01-13T14:44:47+0000 DDEBUG Command: dnf --installroot=/tmp/root swap -y openssl-libs openssl-snapsafe-libs 
2025-01-13T14:44:47+0000 DDEBUG Installroot: /tmp/root
2025-01-13T14:44:47+0000 DDEBUG Releasever: 2023.6.20250107
2025-01-13T14:44:47+0000 DEBUG cachedir: /tmp/root/var/cache/dnf
2025-01-13T14:44:47+0000 DDEBUG Base command: swap
2025-01-13T14:44:47+0000 DDEBUG Extra commands: ['--installroot=/tmp/root', 'swap', '-y', 'openssl-libs', 'openssl-snapsafe-libs']
2025-01-13T14:44:47+0000 DEBUG User-Agent: constructed: 'libdnf (Amazon Linux 2023; generic; Linux.aarch64)'
2025-01-13T14:44:47+0000 DEBUG repo: using cache for: amazonlinux
2025-01-13T14:44:47+0000 DEBUG amazonlinux: using metadata from Mon Jan  6 00:00:00 2025.
2025-01-13T14:44:47+0000 INFO Last metadata expiration check: 0:00:09 ago on Mon Jan 13 14:44:38 2025.
2025-01-13T14:44:47+0000 DDEBUG timer: sack setup: 109 ms
2025-01-13T14:44:48+0000 DEBUG --> Starting dependency resolution
2025-01-13T14:44:48+0000 DEBUG --> Finding unneeded leftover dependencies
2025-01-13T14:44:48+0000 DEBUG ---> Package openssl-snapsafe-libs.aarch64 1:3.0.8-1.amzn2023.0.18 will be installed
2025-01-13T14:44:48+0000 DEBUG ---> Package openssl-libs.aarch64 1:3.0.8-1.amzn2023.0.18 will be erased
2025-01-13T14:44:48+0000 DEBUG --> Finished dependency resolution
2025-01-13T14:44:48+0000 DDEBUG timer: depsolve: 33 ms
2025-01-13T14:44:48+0000 INFO Dependencies resolved.
2025-01-13T14:44:48+0000 INFO ================================================================================
 Package                Arch     Version                     Repository    Size
================================================================================
Installing:
 openssl-snapsafe-libs  aarch64  1:3.0.8-1.amzn2023.0.18     amazonlinux  2.1 M
Removing:
 openssl-libs           aarch64  1:3.0.8-1.amzn2023.0.18     @System      7.0 M

Transaction Summary
================================================================================
Install  1 Package
Remove   1 Package

2025-01-13T14:44:48+0000 INFO Total download size: 2.1 M
2025-01-13T14:44:48+0000 INFO Downloading Packages:
2025-01-13T14:44:48+0000 INFO --------------------------------------------------------------------------------
2025-01-13T14:44:48+0000 INFO Total                                           6.1 MB/s | 2.1 MB     00:00     
2025-01-13T14:44:48+0000 DEBUG Using rpmkeys executable at /usr/bin/rpmkeys to verify signatures
2025-01-13T14:44:48+0000 INFO Running transaction check
2025-01-13T14:44:48+0000 INFO Transaction check succeeded.
2025-01-13T14:44:48+0000 INFO Running transaction test
2025-01-13T14:44:48+0000 INFO Transaction test succeeded.
2025-01-13T14:44:48+0000 DDEBUG timer: transaction test: 21 ms
2025-01-13T14:44:48+0000 INFO Running transaction
2025-01-13T14:44:48+0000 DDEBUG RPM transaction start.
2025-01-13T14:44:48+0000 DDEBUG RPM transaction over.
2025-01-13T14:44:48+0000 DDEBUG timer: verify transaction: 15 ms
2025-01-13T14:44:48+0000 DDEBUG timer: transaction: 414 ms
2025-01-13T14:44:48+0000 DEBUG Installed: openssl-snapsafe-libs-1:3.0.8-1.amzn2023.0.18.aarch64
2025-01-13T14:44:48+0000 DEBUG Removed: openssl-libs-1:3.0.8-1.amzn2023.0.18.aarch64
2025-01-13T14:44:48+0000 INFO Complete!
2025-01-13T14:44:48+0000 DDEBUG Cleaning up.
2025-01-13T14:44:48+0000 DDEBUG /tmp/root/var/cache/dnf/amazonlinux-794d0ccad44a7436/packages/openssl-snapsafe-libs-3.0.8-1.amzn2023.0.18.aarch64.rpm removed
2025-01-13T14:45:01+0000 INFO --- logging initialized ---
2025-01-13T14:45:01+0000 DDEBUG timer: config: 1 ms
2025-01-13T14:45:01+0000 DEBUG YUM version: 4.14.0
2025-01-13T14:45:01+0000 DDEBUG Command: yum deplist --installroot=/tmp/root crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch pcre2-syntax-10.40-1.amzn2023.0.3.noarch ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64 bash-5.2.15-1.amzn2023.0.2.aarch64 setup-2.13.7-3.amzn2023.0.2.noarch basesystem-11-11.amzn2023.0.2.noarch glibc-common-2.34-117.amzn2023.0.1.aarch64 zlib-1.2.11-33.amzn2023.0.5.aarch64 xz-libs-5.2.5-9.amzn2023.0.2.aarch64 bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64 libzstd-1.5.5-1.amzn2023.0.1.aarch64 p11-kit-0.24.1-2.amzn2023.0.3.aarch64 sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64 libattr-2.5.1-3.amzn2023.0.2.aarch64 libcap-2.48-2.amzn2023.0.3.aarch64 libunistring-0.9.10-10.amzn2023.0.2.aarch64 libuuid-2.37.4-1.amzn2023.0.4.aarch64 readline-8.1-2.amzn2023.0.2.aarch64 mpfr-4.1.0-7.amzn2023.0.2.aarch64 libgcrypt-1.10.2-1.amzn2023.0.2.aarch64 alternatives-1.15-2.amzn2023.0.2.aarch64 keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64 audit-libs-3.0.6-1.amzn2023.0.2.aarch64 libnghttp2-1.59.0-3.amzn2023.0.1.aarch64 libselinux-3.4-5.amzn2023.0.2.aarch64 sed-4.8-7.amzn2023.0.2.aarch64 libmount-2.37.4-1.amzn2023.0.4.aarch64 gawk-5.1.0-3.amzn2023.0.3.aarch64 libtasn1-4.19.0-1.amzn2023.0.4.aarch64 ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch glib2-2.82.2-764.amzn2023.aarch64 libpeas-1.32.0-1.amzn2023.0.3.aarch64 krb5-libs-1.21.3-1.amzn2023.0.1.aarch64 curl-minimal-8.5.0-1.amzn2023.0.4.aarch64 lua-libs-5.4.4-3.amzn2023.0.2.aarch64 libarchive-3.7.4-2.amzn2023.0.2.aarch64 rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64 libsolv-0.7.22-1.amzn2023.0.2.aarch64 gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64 librepo-1.14.5-2.amzn2023.0.1.aarch64 libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch microdnf-3.10.0-2.amzn2023.0.1.aarch64 libgcc-11.4.1-2.amzn2023.0.2.aarch64 publicsuffix-list-dafsa-20240212-61.amzn2023.noarch ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch system-release-2023.6.20250107-0.amzn2023.noarch filesystem-3.14-5.amzn2023.0.3.aarch64 glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64 glibc-2.34-117.amzn2023.0.1.aarch64 libgpg-error-1.42-1.amzn2023.0.2.aarch64 libxml2-2.10.4-1.amzn2023.0.7.aarch64 libffi-3.4.4-1.amzn2023.0.1.aarch64 pcre2-10.40-1.amzn2023.0.3.aarch64 libassuan-2.5.5-1.amzn2023.0.2.aarch64 gmp-6.2.1-2.amzn2023.0.2.aarch64 libacl-2.3.1-2.amzn2023.0.2.aarch64 libsmartcols-2.37.4-1.amzn2023.0.4.aarch64 libidn2-2.3.2-1.amzn2023.0.5.aarch64 popt-1.18-6.amzn2023.0.2.aarch64 libpsl-0.21.1-3.amzn2023.0.2.aarch64 grep-3.8-1.amzn2023.0.4.aarch64 file-libs-5.39-7.amzn2023.0.4.aarch64 json-c-0.14-8.amzn2023.0.2.aarch64 libcap-ng-0.8.2-4.amzn2023.0.2.aarch64 libcom_err-1.46.5-2.amzn2023.0.2.aarch64 libsepol-3.4-3.amzn2023.0.3.aarch64 coreutils-single-8.32-30.amzn2023.0.3.aarch64 libblkid-2.37.4-1.amzn2023.0.4.aarch64 libsigsegv-2.13-2.amzn2023.0.2.aarch64 libstdc++-11.4.1-2.amzn2023.0.2.aarch64 p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64 gobject-introspection-1.82.0-1.amzn2023.aarch64 libverto-0.3.2-1.amzn2023.0.2.aarch64 libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64 libyaml-0.2.5-5.amzn2023.0.2.aarch64 lz4-libs-1.9.4-1.amzn2023.0.2.aarch64 rpm-4.16.1.3-29.amzn2023.0.6.aarch64 libmodulemd-2.13.0-2.amzn2023.0.2.aarch64 npth-1.6-6.amzn2023.0.2.aarch64 gpgme-1.15.1-6.amzn2023.0.3.aarch64 libdnf-0.69.0-8.amzn2023.0.5.aarch64 dnf-data-4.14.0-1.amzn2023.0.5.noarch microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64 gpg-pubkey-d832c631-6515c85e.(none) tzdata-2024a-1.amzn2023.0.1.noarch openssl-snapsafe-libs-3.0.8-1.amzn2023.0.18.aarch64 
2025-01-13T14:45:01+0000 DDEBUG Installroot: /tmp/root
2025-01-13T14:45:01+0000 DDEBUG Releasever: 2023.6.20250107
2025-01-13T14:45:01+0000 DEBUG cachedir: /tmp/root/var/cache/dnf
2025-01-13T14:45:01+0000 DDEBUG Base command: deplist
2025-01-13T14:45:01+0000 DDEBUG Extra commands: ['deplist', '--installroot=/tmp/root', 'crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch', 'pcre2-syntax-10.40-1.amzn2023.0.3.noarch', 'ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64', 'bash-5.2.15-1.amzn2023.0.2.aarch64', 'setup-2.13.7-3.amzn2023.0.2.noarch', 'basesystem-11-11.amzn2023.0.2.noarch', 'glibc-common-2.34-117.amzn2023.0.1.aarch64', 'zlib-1.2.11-33.amzn2023.0.5.aarch64', 'xz-libs-5.2.5-9.amzn2023.0.2.aarch64', 'bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64', 'libzstd-1.5.5-1.amzn2023.0.1.aarch64', 'p11-kit-0.24.1-2.amzn2023.0.3.aarch64', 'sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64', 'libattr-2.5.1-3.amzn2023.0.2.aarch64', 'libcap-2.48-2.amzn2023.0.3.aarch64', 'libunistring-0.9.10-10.amzn2023.0.2.aarch64', 'libuuid-2.37.4-1.amzn2023.0.4.aarch64', 'readline-8.1-2.amzn2023.0.2.aarch64', 'mpfr-4.1.0-7.amzn2023.0.2.aarch64', 'libgcrypt-1.10.2-1.amzn2023.0.2.aarch64', 'alternatives-1.15-2.amzn2023.0.2.aarch64', 'keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64', 'audit-libs-3.0.6-1.amzn2023.0.2.aarch64', 'libnghttp2-1.59.0-3.amzn2023.0.1.aarch64', 'libselinux-3.4-5.amzn2023.0.2.aarch64', 'sed-4.8-7.amzn2023.0.2.aarch64', 'libmount-2.37.4-1.amzn2023.0.4.aarch64', 'gawk-5.1.0-3.amzn2023.0.3.aarch64', 'libtasn1-4.19.0-1.amzn2023.0.4.aarch64', 'ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch', 'glib2-2.82.2-764.amzn2023.aarch64', 'libpeas-1.32.0-1.amzn2023.0.3.aarch64', 'krb5-libs-1.21.3-1.amzn2023.0.1.aarch64', 'curl-minimal-8.5.0-1.amzn2023.0.4.aarch64', 'lua-libs-5.4.4-3.amzn2023.0.2.aarch64', 'libarchive-3.7.4-2.amzn2023.0.2.aarch64', 'rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64', 'libsolv-0.7.22-1.amzn2023.0.2.aarch64', 'gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64', 'librepo-1.14.5-2.amzn2023.0.1.aarch64', 'libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch', 'microdnf-3.10.0-2.amzn2023.0.1.aarch64', 'libgcc-11.4.1-2.amzn2023.0.2.aarch64', 'publicsuffix-list-dafsa-20240212-61.amzn2023.noarch', 'ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch', 'amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch', 'system-release-2023.6.20250107-0.amzn2023.noarch', 'filesystem-3.14-5.amzn2023.0.3.aarch64', 'glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64', 'glibc-2.34-117.amzn2023.0.1.aarch64', 'libgpg-error-1.42-1.amzn2023.0.2.aarch64', 'libxml2-2.10.4-1.amzn2023.0.7.aarch64', 'libffi-3.4.4-1.amzn2023.0.1.aarch64', 'pcre2-10.40-1.amzn2023.0.3.aarch64', 'libassuan-2.5.5-1.amzn2023.0.2.aarch64', 'gmp-6.2.1-2.amzn2023.0.2.aarch64', 'libacl-2.3.1-2.amzn2023.0.2.aarch64', 'libsmartcols-2.37.4-1.amzn2023.0.4.aarch64', 'libidn2-2.3.2-1.amzn2023.0.5.aarch64', 'popt-1.18-6.amzn2023.0.2.aarch64', 'libpsl-0.21.1-3.amzn2023.0.2.aarch64', 'grep-3.8-1.amzn2023.0.4.aarch64', 'file-libs-5.39-7.amzn2023.0.4.aarch64', 'json-c-0.14-8.amzn2023.0.2.aarch64', 'libcap-ng-0.8.2-4.amzn2023.0.2.aarch64', 'libcom_err-1.46.5-2.amzn2023.0.2.aarch64', 'libsepol-3.4-3.amzn2023.0.3.aarch64', 'coreutils-single-8.32-30.amzn2023.0.3.aarch64', 'libblkid-2.37.4-1.amzn2023.0.4.aarch64', 'libsigsegv-2.13-2.amzn2023.0.2.aarch64', 'libstdc++-11.4.1-2.amzn2023.0.2.aarch64', 'p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64', 'gobject-introspection-1.82.0-1.amzn2023.aarch64', 'libverto-0.3.2-1.amzn2023.0.2.aarch64', 'libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64', 'libyaml-0.2.5-5.amzn2023.0.2.aarch64', 'lz4-libs-1.9.4-1.amzn2023.0.2.aarch64', 'rpm-4.16.1.3-29.amzn2023.0.6.aarch64', 'libmodulemd-2.13.0-2.amzn2023.0.2.aarch64', 'npth-1.6-6.amzn2023.0.2.aarch64', 'gpgme-1.15.1-6.amzn2023.0.3.aarch64', 'libdnf-0.69.0-8.amzn2023.0.5.aarch64', 'dnf-data-4.14.0-1.amzn2023.0.5.noarch', 'microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64', 'gpg-pubkey-d832c631-6515c85e.(none)', 'tzdata-2024a-1.amzn2023.0.1.noarch', 'openssl-snapsafe-libs-3.0.8-1.amzn2023.0.18.aarch64']
2025-01-13T14:45:01+0000 DEBUG User-Agent: constructed: 'libdnf (Amazon Linux 2023; generic; Linux.aarch64)'
2025-01-13T14:45:01+0000 DEBUG repo: using cache for: amazonlinux
2025-01-13T14:45:01+0000 DEBUG amazonlinux: using metadata from Mon Jan  6 00:00:00 2025.
2025-01-13T14:45:01+0000 INFO Last metadata expiration check: 0:00:23 ago on Mon Jan 13 14:44:38 2025.
2025-01-13T14:45:01+0000 DDEBUG timer: sack setup: 113 ms
2025-01-13T14:45:03+0000 DDEBUG Cleaning up.
2025-01-13T14:45:22+0000 INFO --- logging initialized ---
2025-01-13T14:45:22+0000 DDEBUG timer: config: 1 ms
2025-01-13T14:45:22+0000 DEBUG YUM version: 4.14.0
2025-01-13T14:45:22+0000 DDEBUG Command: yum deplist --installroot=/tmp/root crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch pcre2-syntax-10.40-1.amzn2023.0.3.noarch ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64 bash-5.2.15-1.amzn2023.0.2.aarch64 setup-2.13.7-3.amzn2023.0.2.noarch basesystem-11-11.amzn2023.0.2.noarch glibc-common-2.34-117.amzn2023.0.1.aarch64 zlib-1.2.11-33.amzn2023.0.5.aarch64 xz-libs-5.2.5-9.amzn2023.0.2.aarch64 bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64 libzstd-1.5.5-1.amzn2023.0.1.aarch64 p11-kit-0.24.1-2.amzn2023.0.3.aarch64 sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64 libattr-2.5.1-3.amzn2023.0.2.aarch64 libcap-2.48-2.amzn2023.0.3.aarch64 libunistring-0.9.10-10.amzn2023.0.2.aarch64 libuuid-2.37.4-1.amzn2023.0.4.aarch64 readline-8.1-2.amzn2023.0.2.aarch64 mpfr-4.1.0-7.amzn2023.0.2.aarch64 libgcrypt-1.10.2-1.amzn2023.0.2.aarch64 alternatives-1.15-2.amzn2023.0.2.aarch64 keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64 audit-libs-3.0.6-1.amzn2023.0.2.aarch64 libnghttp2-1.59.0-3.amzn2023.0.1.aarch64 libselinux-3.4-5.amzn2023.0.2.aarch64 sed-4.8-7.amzn2023.0.2.aarch64 libmount-2.37.4-1.amzn2023.0.4.aarch64 gawk-5.1.0-3.amzn2023.0.3.aarch64 libtasn1-4.19.0-1.amzn2023.0.4.aarch64 ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch glib2-2.82.2-764.amzn2023.aarch64 libpeas-1.32.0-1.amzn2023.0.3.aarch64 krb5-libs-1.21.3-1.amzn2023.0.1.aarch64 curl-minimal-8.5.0-1.amzn2023.0.4.aarch64 lua-libs-5.4.4-3.amzn2023.0.2.aarch64 libarchive-3.7.4-2.amzn2023.0.2.aarch64 rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64 libsolv-0.7.22-1.amzn2023.0.2.aarch64 gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64 librepo-1.14.5-2.amzn2023.0.1.aarch64 libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch microdnf-3.10.0-2.amzn2023.0.1.aarch64 libgcc-11.4.1-2.amzn2023.0.2.aarch64 publicsuffix-list-dafsa-20240212-61.amzn2023.noarch ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch system-release-2023.6.20250107-0.amzn2023.noarch filesystem-3.14-5.amzn2023.0.3.aarch64 glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64 glibc-2.34-117.amzn2023.0.1.aarch64 libgpg-error-1.42-1.amzn2023.0.2.aarch64 libxml2-2.10.4-1.amzn2023.0.7.aarch64 libffi-3.4.4-1.amzn2023.0.1.aarch64 pcre2-10.40-1.amzn2023.0.3.aarch64 libassuan-2.5.5-1.amzn2023.0.2.aarch64 gmp-6.2.1-2.amzn2023.0.2.aarch64 libacl-2.3.1-2.amzn2023.0.2.aarch64 libsmartcols-2.37.4-1.amzn2023.0.4.aarch64 libidn2-2.3.2-1.amzn2023.0.5.aarch64 popt-1.18-6.amzn2023.0.2.aarch64 libpsl-0.21.1-3.amzn2023.0.2.aarch64 grep-3.8-1.amzn2023.0.4.aarch64 file-libs-5.39-7.amzn2023.0.4.aarch64 json-c-0.14-8.amzn2023.0.2.aarch64 libcap-ng-0.8.2-4.amzn2023.0.2.aarch64 libcom_err-1.46.5-2.amzn2023.0.2.aarch64 libsepol-3.4-3.amzn2023.0.3.aarch64 coreutils-single-8.32-30.amzn2023.0.3.aarch64 libblkid-2.37.4-1.amzn2023.0.4.aarch64 libsigsegv-2.13-2.amzn2023.0.2.aarch64 libstdc++-11.4.1-2.amzn2023.0.2.aarch64 p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64 gobject-introspection-1.82.0-1.amzn2023.aarch64 libverto-0.3.2-1.amzn2023.0.2.aarch64 libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64 libyaml-0.2.5-5.amzn2023.0.2.aarch64 lz4-libs-1.9.4-1.amzn2023.0.2.aarch64 rpm-4.16.1.3-29.amzn2023.0.6.aarch64 libmodulemd-2.13.0-2.amzn2023.0.2.aarch64 npth-1.6-6.amzn2023.0.2.aarch64 gpgme-1.15.1-6.amzn2023.0.3.aarch64 libdnf-0.69.0-8.amzn2023.0.5.aarch64 dnf-data-4.14.0-1.amzn2023.0.5.noarch microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64 gpg-pubkey-d832c631-6515c85e.(none) tzdata-2024a-1.amzn2023.0.1.noarch openssl-snapsafe-libs-3.0.8-1.amzn2023.0.18.aarch64 
2025-01-13T14:45:22+0000 DDEBUG Installroot: /tmp/root
2025-01-13T14:45:22+0000 DDEBUG Releasever: 2023.6.20250107
2025-01-13T14:45:22+0000 DEBUG cachedir: /tmp/root/var/cache/dnf
2025-01-13T14:45:22+0000 DDEBUG Base command: deplist
2025-01-13T14:45:22+0000 DDEBUG Extra commands: ['deplist', '--installroot=/tmp/root', 'crypto-policies-20220428-1.gitdfb10ea.amzn2023.0.2.noarch', 'pcre2-syntax-10.40-1.amzn2023.0.3.noarch', 'ncurses-libs-6.2-4.20200222.amzn2023.0.6.aarch64', 'bash-5.2.15-1.amzn2023.0.2.aarch64', 'setup-2.13.7-3.amzn2023.0.2.noarch', 'basesystem-11-11.amzn2023.0.2.noarch', 'glibc-common-2.34-117.amzn2023.0.1.aarch64', 'zlib-1.2.11-33.amzn2023.0.5.aarch64', 'xz-libs-5.2.5-9.amzn2023.0.2.aarch64', 'bzip2-libs-1.0.8-6.amzn2023.0.2.aarch64', 'libzstd-1.5.5-1.amzn2023.0.1.aarch64', 'p11-kit-0.24.1-2.amzn2023.0.3.aarch64', 'sqlite-libs-3.40.0-1.amzn2023.0.4.aarch64', 'libattr-2.5.1-3.amzn2023.0.2.aarch64', 'libcap-2.48-2.amzn2023.0.3.aarch64', 'libunistring-0.9.10-10.amzn2023.0.2.aarch64', 'libuuid-2.37.4-1.amzn2023.0.4.aarch64', 'readline-8.1-2.amzn2023.0.2.aarch64', 'mpfr-4.1.0-7.amzn2023.0.2.aarch64', 'libgcrypt-1.10.2-1.amzn2023.0.2.aarch64', 'alternatives-1.15-2.amzn2023.0.2.aarch64', 'keyutils-libs-1.6.3-1.amzn2023.0.1.aarch64', 'audit-libs-3.0.6-1.amzn2023.0.2.aarch64', 'libnghttp2-1.59.0-3.amzn2023.0.1.aarch64', 'libselinux-3.4-5.amzn2023.0.2.aarch64', 'sed-4.8-7.amzn2023.0.2.aarch64', 'libmount-2.37.4-1.amzn2023.0.4.aarch64', 'gawk-5.1.0-3.amzn2023.0.3.aarch64', 'libtasn1-4.19.0-1.amzn2023.0.4.aarch64', 'ca-certificates-2023.2.68-1.0.amzn2023.0.1.noarch', 'glib2-2.82.2-764.amzn2023.aarch64', 'libpeas-1.32.0-1.amzn2023.0.3.aarch64', 'krb5-libs-1.21.3-1.amzn2023.0.1.aarch64', 'curl-minimal-8.5.0-1.amzn2023.0.4.aarch64', 'lua-libs-5.4.4-3.amzn2023.0.2.aarch64', 'libarchive-3.7.4-2.amzn2023.0.2.aarch64', 'rpm-libs-4.16.1.3-29.amzn2023.0.6.aarch64', 'libsolv-0.7.22-1.amzn2023.0.2.aarch64', 'gnupg2-minimal-2.3.7-1.amzn2023.0.4.aarch64', 'librepo-1.14.5-2.amzn2023.0.1.aarch64', 'libreport-filesystem-2.15.2-2.amzn2023.0.2.noarch', 'microdnf-3.10.0-2.amzn2023.0.1.aarch64', 'libgcc-11.4.1-2.amzn2023.0.2.aarch64', 'publicsuffix-list-dafsa-20240212-61.amzn2023.noarch', 'ncurses-base-6.2-4.20200222.amzn2023.0.6.noarch', 'amazon-linux-repo-cdn-2023.6.20250107-0.amzn2023.noarch', 'system-release-2023.6.20250107-0.amzn2023.noarch', 'filesystem-3.14-5.amzn2023.0.3.aarch64', 'glibc-minimal-langpack-2.34-117.amzn2023.0.1.aarch64', 'glibc-2.34-117.amzn2023.0.1.aarch64', 'libgpg-error-1.42-1.amzn2023.0.2.aarch64', 'libxml2-2.10.4-1.amzn2023.0.7.aarch64', 'libffi-3.4.4-1.amzn2023.0.1.aarch64', 'pcre2-10.40-1.amzn2023.0.3.aarch64', 'libassuan-2.5.5-1.amzn2023.0.2.aarch64', 'gmp-6.2.1-2.amzn2023.0.2.aarch64', 'libacl-2.3.1-2.amzn2023.0.2.aarch64', 'libsmartcols-2.37.4-1.amzn2023.0.4.aarch64', 'libidn2-2.3.2-1.amzn2023.0.5.aarch64', 'popt-1.18-6.amzn2023.0.2.aarch64', 'libpsl-0.21.1-3.amzn2023.0.2.aarch64', 'grep-3.8-1.amzn2023.0.4.aarch64', 'file-libs-5.39-7.amzn2023.0.4.aarch64', 'json-c-0.14-8.amzn2023.0.2.aarch64', 'libcap-ng-0.8.2-4.amzn2023.0.2.aarch64', 'libcom_err-1.46.5-2.amzn2023.0.2.aarch64', 'libsepol-3.4-3.amzn2023.0.3.aarch64', 'coreutils-single-8.32-30.amzn2023.0.3.aarch64', 'libblkid-2.37.4-1.amzn2023.0.4.aarch64', 'libsigsegv-2.13-2.amzn2023.0.2.aarch64', 'libstdc++-11.4.1-2.amzn2023.0.2.aarch64', 'p11-kit-trust-0.24.1-2.amzn2023.0.3.aarch64', 'gobject-introspection-1.82.0-1.amzn2023.aarch64', 'libverto-0.3.2-1.amzn2023.0.2.aarch64', 'libcurl-minimal-8.5.0-1.amzn2023.0.4.aarch64', 'libyaml-0.2.5-5.amzn2023.0.2.aarch64', 'lz4-libs-1.9.4-1.amzn2023.0.2.aarch64', 'rpm-4.16.1.3-29.amzn2023.0.6.aarch64', 'libmodulemd-2.13.0-2.amzn2023.0.2.aarch64', 'npth-1.6-6.amzn2023.0.2.aarch64', 'gpgme-1.15.1-6.amzn2023.0.3.aarch64', 'libdnf-0.69.0-8.amzn2023.0.5.aarch64', 'dnf-data-4.14.0-1.amzn2023.0.5.noarch', 'microdnf-dnf-3.10.0-2.amzn2023.0.1.aarch64', 'gpg-pubkey-d832c631-6515c85e.(none)', 'tzdata-2024a-1.amzn2023.0.1.noarch', 'openssl-snapsafe-libs-3.0.8-1.amzn2023.0.18.aarch64']
2025-01-13T14:45:22+0000 DEBUG User-Agent: constructed: 'libdnf (Amazon Linux 2023; generic; Linux.aarch64)'
2025-01-13T14:45:22+0000 DEBUG repo: using cache for: amazonlinux
2025-01-13T14:45:22+0000 DEBUG amazonlinux: using metadata from Mon Jan  6 00:00:00 2025.
2025-01-13T14:45:22+0000 INFO Last metadata expiration check: 0:00:44 ago on Mon Jan 13 14:44:38 2025.
2025-01-13T14:45:22+0000 DDEBUG timer: sack setup: 104 ms
2025-01-13T14:45:24+0000 DDEBUG Cleaning up.
