#!/usr/bin/sh

#set -vx

# At this time, while this script is trivial, we ignore any parameters given.
# However, for backwards compatibility reasons, future versions of this script must 
# support the syntax "update-ca-trust extract" trigger the generation of output 
# files in $DEST.

DEST=/etc/pki/ca-trust/extracted

# Prevent p11-kit from reading user configuration files.
export P11_KIT_NO_USER_CONFIG=1

# OpenSSL PEM bundle that includes trust flags
# (BEGIN TRUSTED CERTIFICATE)
/usr/bin/p11-kit extract --format=openssl-bundle --filter=certificates --overwrite --comment $DEST/openssl/ca-bundle.trust.crt
/usr/bin/p11-kit extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose server-auth $DEST/pem/tls-ca-bundle.pem
/usr/bin/p11-kit extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose email $DEST/pem/email-ca-bundle.pem
/usr/bin/p11-kit extract --format=pem-bundle --filter=ca-anchors --overwrite --comment --purpose code-signing $DEST/pem/objsign-ca-bundle.pem
/usr/bin/p11-kit extract --format=java-cacerts --filter=ca-anchors --overwrite --purpose server-auth $DEST/java/cacerts
/usr/bin/p11-kit extract --format=edk2-cacerts --filter=ca-anchors --overwrite --purpose=server-auth $DEST/edk2/cacerts.bin
# Hashed directory of BEGIN TRUSTED-style certs (usable as OpenSSL CApath and
# by GnuTLS)
/usr/bin/p11-kit extract --format=pem-directory-hash --filter=ca-anchors --overwrite --purpose server-auth $DEST/pem/directory-hash
# Debian compatibility: their /etc/ssl/certs has this bundle
/usr/bin/ln -s ../tls-ca-bundle.pem $DEST/pem/directory-hash/ca-certificates.crt
# Backwards compatibility: RHEL/Fedora provided a /etc/ssl/certs/ca-bundle.crt
# since https://bugzilla.redhat.com/show_bug.cgi?id=572725
/usr/bin/ln -s ../tls-ca-bundle.pem $DEST/pem/directory-hash/ca-bundle.crt

