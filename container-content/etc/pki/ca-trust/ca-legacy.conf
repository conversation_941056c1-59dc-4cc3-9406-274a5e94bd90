# The upstream Mozilla.org project tests all changes to the root CA
# list with the NSS (Network Security Services) library.
#
# Occassionally, changes might cause compatibility issues with
# other cryptographic libraries, such as openssl or gnutls.
#
# The package maintainers of the CA certificates package might decide
# to temporarily keep certain (legacy) root CA certificates trusted,
# until incompatibility issues can be resolved.
# 
# Using this configuration file it is possible to opt-out of the
# compatibility choices made by the package maintainer.
#
# legacy=default :
#   This configuration uses the choices made by the package maintainer.
#   It may keep root CA certificate as trusted, which the upstream 
#   Mozilla.org project has already marked as no longer trusted.
#   The set of CA certificates that are being kept enabled may change
#   between package versions.
#
# legacy=disable :
#   Follow all removal decisions made by Mozilla.org
#
legacy=default
