EVENT=notify component=dnf
        # there has to be a comment here, otherwise
        # the next line is interpreted as a condition
        function fetch()
        {
            for log in $*; do
            new_name=${log//\//_}
            cp $log $new_name
            done
        }

        logs=`find /var/cache/dnf -iname '*.log'`
        fetch $logs
        fetch /var/log/dnf.log
        # this would fail for a non-priviledged user
        journalctl _SYSTEMD_UNIT=dnf-makecache.service &>dnf-makecache.log
        fetch /var/log/dnf.transaction.log
        if [[ -r username ]]; then
            username=`cat username`
            if [[ $username != "root" ]]; then
            logs=`find /var/tmp -path "/var/tmp/dnf-${username}-*" -iname '*.log'`
            fetch $logs
            fi
        fi
