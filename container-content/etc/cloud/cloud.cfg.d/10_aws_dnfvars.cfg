# ### DO NOT MODIFY THIS FILE! ###
# This file will be replaced if cloud-init is upgraded.
# Please put your modifications in other files under /etc/cloud/cloud.cfg.d/
#
# Note that cloud-init uses flexible merge strategies for config options
# https://cloudinit.readthedocs.org/en/latest/topics/merging.html

write_metadata:
  # Fill in yum vars for the region and domain
  - path: /etc/dnf/vars/awsregion
    data:
      - identity: region
      - "default"
  - path: /etc/dnf/vars/awsdomain
    data:
      - metadata: services/domain
      - "amazonaws.com"

# vim:syntax=yaml expandtab
