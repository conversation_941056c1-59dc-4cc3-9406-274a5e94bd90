#
# Ethernet frame types
#		This file describes some of the various Ethernet
#		protocol types that are used on Ethernet networks.
#
# This list could be found on:
#         http://www.iana.org/assignments/ethernet-numbers
#         http://www.iana.org/assignments/ieee-802-numbers
#
# <name>    <hexnumber> <alias1>...<alias35> #Comment
#
IPv4	 	0800  	ip ip4 		# Internet IP (IPv4)
X25		0805
ARP		0806	ether-arp	#
FR_ARP		0808    		# Frame Relay ARP        [RFC1701]
BPQ		08FF			# G8BPQ AX.25 Ethernet Packet
DEC		6000			# DEC Assigned proto
DNA_DL		6001			# DEC DNA Dump/Load
DNA_RC		6002			# DEC DNA Remote Console
DNA_RT		6003			# DEC DNA Routing
LAT		6004			# DEC LAT
DIAG		6005			# DEC Diagnostics
CUST		6006			# DEC Customer use
SCA		6007			# DEC Systems Comms Arch
TEB		6558			# Trans Ether Bridging   [RFC1701]
RAW_FR  	6559			# Raw Frame Relay        [RFC1701]
RARP		8035			# Reverse ARP            [RFC903]
AARP		80F3			# Appletalk AARP
ATALK		809B			# Appletalk
802_1Q		8100	8021q 1q 802.1q	dot1q # 802.1Q Virtual LAN tagged frame
IPX		8137			# Novell IPX
NetBEUI		8191			# NetBEUI
IPv6		86DD	ip6 		# IP version 6
PPP		880B			# PPP
ATMMPOA		884C			# MultiProtocol over ATM
PPP_DISC	8863			# PPPoE discovery messages
PPP_SES		8864			# PPPoE session messages
ATMFATE		8884			# Frame-based ATM Transport over Ethernet
LOOP		9000	loopback 	# loop proto
