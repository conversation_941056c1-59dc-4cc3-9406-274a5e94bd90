FROM public.ecr.aws/lambda/python:3.12.2025.01.15.17 as build-image
COPY requirements.txt ./tmp/requirements.txt
COPY ./src ./src
RUN pip install 'uvicorn[standard]'
RUN pip install 'vanna[openai,mysql]'
RUN pip install --no-cache-dir -r ./tmp/requirements.txt
RUN chown -R 1000:1000 ./src
USER 1000
ENV MYSQL_HOST=audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com
ENV MYSQL_DB=audit_gold
ENV MYSQL_USER=devdb
ENV MYSQL_PASSWORD='jtNr6=LdR+R6aF4-d~J'
ENV MYSQL_PORT=3306
ENV VANNA_API_KEY=13db2c77c50749d7b338a9b105854ea8
ENV OPENAI_API_KEY=********************************************************************************************************************************************************************
ENV HOME=/tmp
ENV DEEPSEEK_API_KEY=***********************************
ENV MODEL_SQL=gpt-4.1-mini
CMD ["src.app.handler"]