# Dockerfile reconstruído baseado na inspeção da imagem original
# Imagem base: AWS Lambda Python 3.12 para ARM64
FROM public.ecr.aws/lambda/python:3.12-arm64

# Definir diretório de trabalho (já definido na imagem base)
WORKDIR /var/task

# Copiar arquivo de requirements primeiro (para aproveitar cache do Docker)
COPY requirements.txt ./tmp/requirements.txt

# Copiar código fonte da aplicação
COPY ./src ./src

# Instalar uvicorn com dependências padrão
RUN pip install 'uvicorn[standard]'

# Instalar vanna com suporte para OpenAI e MySQL
RUN pip install 'vanna[openai,mysql]'

# Instalar dependências do requirements.txt
RUN pip install --no-cache-dir -r ./tmp/requirements.txt

# Alterar ownership dos arquivos para o usuário 1000
RUN chown -R 1000:1000 ./src

# Definir usuário não-root
USER 1000

# Variáveis de ambiente de configuração do banco de dados
ENV MYSQL_HOST=audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com
ENV MYSQL_DB=audit_gold
ENV MYSQL_USER=devdb
ENV MYSQL_PASSWORD=jtNr6=LdR+R6aF4-d~J
ENV MYSQL_PORT=3306

# Chaves de API
ENV VANNA_API_KEY=13db2c77c50749d7b338a9b105854ea8
ENV OPENAI_API_KEY=********************************************************************************************************************************************************************
ENV DEEPSEEK_API_KEY=***********************************

# Configurações adicionais
ENV HOME=/tmp
ENV MODEL_SQL=gpt-4.1-mini

# Comando padrão para executar a função Lambda
CMD ["src.app.handler"]
