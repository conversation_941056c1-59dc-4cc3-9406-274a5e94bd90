{"architecture": "arm64", "config": {"User": "1000", "Env": ["LANG=en_US.UTF-8", "TZ=:/etc/localtime", "PATH=/var/lang/bin:/usr/local/bin:/usr/bin/:/bin:/opt/bin", "LD_LIBRARY_PATH=/var/lang/lib:/lib64:/usr/lib64:/var/runtime:/var/runtime/lib:/var/task:/var/task/lib:/opt/lib", "LAMBDA_TASK_ROOT=/var/task", "LAMBDA_RUNTIME_DIR=/var/runtime", "MYSQL_HOST=audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com", "MYSQL_DB=audit_gold", "MYSQL_USER=devdb", "MYSQL_PASSWORD=jtNr6=LdR+R6aF4-d~J", "MYSQL_PORT=3306", "VANNA_API_KEY=13db2c77c50749d7b338a9b105854ea8", "OPENAI_API_KEY=********************************************************************************************************************************************************************", "HOME=/tmp", "DEEPSEEK_API_KEY=***********************************", "MODEL_SQL=gpt-4.1-mini"], "Entrypoint": ["/lambda-entrypoint.sh"], "Cmd": ["src.app.handler"], "WorkingDir": "/var/task", "Labels": {"com.amazonaws.lambda.platform.kernel": "k510ga"}, "ArgsEscaped": true, "OnBuild": null}, "created": "2025-06-10T14:23:13.425341053Z", "history": [{"created": "2025-01-15T17:41:15Z", "created_by": "ARCHITECTURE arm64", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "VARIANT v8", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "ADD file:c684526a871f32c8d577865b861d002f9fd8b54c6989cbcf0ae993223ba9f3c2 /", "author": "AWS Lambda"}, {"created": "2025-01-15T17:41:15Z", "created_by": "ADD file:33b1468433f2f06ff94530240f76b6f334ccfe58c394b9c4032daa889f3fab7a /", "author": "AWS Lambda"}, {"created": "2025-01-15T17:41:15Z", "created_by": "ADD file:4640a7e40720d148f4f7921410697a4e1abd155f8dce2ea1fb6255a951652299 /", "author": "AWS Lambda"}, {"created": "2025-01-15T17:41:15Z", "created_by": "ADD file:0943ac8b622e8ffcc8a2668542370a47d4c0864f74fc8ae5052b8fe3b81ceb39 /", "author": "AWS Lambda"}, {"created": "2025-01-15T17:41:15Z", "created_by": "ADD file:65f78876afdde97560a6585d730467626a7baf96c9c83bc2c0959c124c9bcdd8 /", "author": "AWS Lambda"}, {"created": "2025-01-15T17:41:15Z", "created_by": "ADD file:e3d0b9d8db022453811963ca57a2de880c30c43407822056340afa974a2f19c4 /", "author": "AWS Lambda"}, {"created": "2025-01-15T17:41:15Z", "created_by": "WORKDIR /var/task", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "ENV LANG=en_US.UTF-8", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "ENV TZ=:/etc/localtime", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "ENV PATH=/var/lang/bin:/usr/local/bin:/usr/bin/:/bin:/opt/bin", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "ENV LD_LIBRARY_PATH=/var/lang/lib:/lib64:/usr/lib64:/var/runtime:/var/runtime/lib:/var/task:/var/task/lib:/opt/lib", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "ENV LAMBDA_TASK_ROOT=/var/task", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "ENV LAMBDA_RUNTIME_DIR=/var/runtime", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:41:15Z", "created_by": "ENTRYPOINT [ \"/lambda-entrypoint.sh\" ]", "author": "AWS Lambda", "empty_layer": true}, {"created": "2025-01-15T17:44:12.501688941Z", "created_by": "/bin/sh -c #(nop)  LABEL com.amazonaws.lambda.platform.kernel=k510ga", "empty_layer": true}, {"created": "2025-05-29T20:08:59.406122844Z", "created_by": "COPY requirements.txt ./tmp/requirements.txt # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-10T14:22:08.171519967Z", "created_by": "COPY ./src ./src # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-10T14:22:15.419807387Z", "created_by": "RUN /bin/sh -c pip install 'uvicorn[standard]' # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-10T14:23:04.941094382Z", "created_by": "RUN /bin/sh -c pip install 'vanna[openai,mysql]' # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-10T14:23:13.016469761Z", "created_by": "RUN /bin/sh -c pip install --no-cache-dir -r ./tmp/requirements.txt # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "RUN /bin/sh -c chown -R 1000:1000 ./src # buildkit", "comment": "buildkit.dockerfile.v0"}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "USER 1000", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV MYSQL_HOST=audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV MYSQL_DB=audit_gold", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV MYSQL_USER=devdb", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV MYSQL_PASSWORD=jtNr6=LdR+R6aF4-d~J", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV MYSQL_PORT=3306", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV VANNA_API_KEY=13db2c77c50749d7b338a9b105854ea8", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV OPENAI_API_KEY=********************************************************************************************************************************************************************", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV HOME=/tmp", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV DEEPSEEK_API_KEY=***********************************", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "ENV MODEL_SQL=gpt-4.1-mini", "comment": "buildkit.dockerfile.v0", "empty_layer": true}, {"created": "2025-06-10T14:23:13.425341053Z", "created_by": "CMD [\"src.app.handler\"]", "comment": "buildkit.dockerfile.v0", "empty_layer": true}], "os": "linux", "rootfs": {"type": "layers", "diff_ids": ["sha256:40e60e04335d139b79290cf7702edc783d25d29621377b43c6ba08fd99111946", "sha256:ca3d64be9bbf33a62a1e1f467216444477879dd9789ba92b89ce9acbcd5bd7c6", "sha256:9ed56d1fe7a144bcb8b5f03824ff3c60b0b8d3410d1da1760ed4800ffe7bce31", "sha256:836f28da8215205bf145d8e6916bac6a31157f9cf6949eacb890f80d4e27851a", "sha256:cb7a74a715a832de353ef333651568ed3519b2d0562d9723d7e7f3d2d273cd79", "sha256:26dcae4c795ee5c2f11c82fabbf628cdaed4e2c0095d64cabef239e60640fa31", "sha256:68c80baaba7c9f0748da5a7a4758d4c6d6ef86fb6d299dc7b5d9e052adafd46a", "sha256:a1e39f06644d7f3324828bad56726844683457ba0eed0d6291d02ac4692b9330", "sha256:fd8093ab6bee7a339e025e4cf52f835d77bbde76b354daedd2ce9957ccddcb7e", "sha256:3372ea7dd8c02f8998ce27fe3f70c15dc7119b8838dd06c3efc2d5e4785c45b9", "sha256:9d99f0651c3966ddba56bc7e6ba17ba6ecff35f74cfbf6ed31f2f3b2dc11a147", "sha256:02ef8ea4a9bce4cc4b935b1e030207e08767b2ea12c2e427eb9502f139e978f7"]}, "variant": "v8"}