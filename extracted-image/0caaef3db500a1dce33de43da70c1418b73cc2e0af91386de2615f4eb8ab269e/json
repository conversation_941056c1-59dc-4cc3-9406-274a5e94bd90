{"id": "0caaef3db500a1dce33de43da70c1418b73cc2e0af91386de2615f4eb8ab269e", "parent": "5e4fa4ee87270e4577a5e76ea7c30dcd192d77e00aeef32f78378f0dd6283543", "created": "2025-06-10T14:23:13.425341053Z", "container_config": {"Hostname": "", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": null, "Cmd": null, "Image": "", "Volumes": null, "WorkingDir": "", "Entrypoint": null, "OnBuild": null, "Labels": null}, "config": {"Hostname": "", "Domainname": "", "User": "1000", "AttachStdin": false, "AttachStdout": false, "AttachStderr": false, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["LANG=en_US.UTF-8", "TZ=:/etc/localtime", "PATH=/var/lang/bin:/usr/local/bin:/usr/bin/:/bin:/opt/bin", "LD_LIBRARY_PATH=/var/lang/lib:/lib64:/usr/lib64:/var/runtime:/var/runtime/lib:/var/task:/var/task/lib:/opt/lib", "LAMBDA_TASK_ROOT=/var/task", "LAMBDA_RUNTIME_DIR=/var/runtime", "MYSQL_HOST=audta-ai.cluster-ro-cdkyomc4a6x4.us-east-1.rds.amazonaws.com", "MYSQL_DB=audit_gold", "MYSQL_USER=devdb", "MYSQL_PASSWORD=jtNr6=LdR+R6aF4-d~J", "MYSQL_PORT=3306", "VANNA_API_KEY=13db2c77c50749d7b338a9b105854ea8", "OPENAI_API_KEY=********************************************************************************************************************************************************************", "HOME=/tmp", "DEEPSEEK_API_KEY=***********************************", "MODEL_SQL=gpt-4.1-mini"], "Cmd": ["src.app.handler"], "ArgsEscaped": true, "Image": "", "Volumes": null, "WorkingDir": "/var/task", "Entrypoint": ["/lambda-entrypoint.sh"], "OnBuild": null, "Labels": {"com.amazonaws.lambda.platform.kernel": "k510ga"}}, "architecture": "arm64", "variant": "v8", "os": "linux"}